-- Roblox DataModel Navigation Script
-- Demonstrates proper Roblox memory hierarchy navigation with hardcoded offsets

-- ========================================
-- HARDCODED OFFSETS AND POINTERS
-- ========================================

-- Core pointers (absolute addresses)
local FAKE_DATAMODEL_POINTER = 0x66EA5E8
local VISUAL_ENGINE_POINTER = 0x6535DD8
local TASK_SCHEDULER_POINTER = 0x67AB9E8
local JOBS_POINTER = 0x67ABBC0
local PLAYER_CONFIGURER_POINTER = 0x66C9D58

-- DataModel navigation offsets
local FAKE_DATAMODEL_TO_DATAMODEL = 0x1B8
local VISUAL_ENGINE_TO_DATAMODEL1 = 0x720
local VISUAL_ENGINE_TO_DATAMODEL2 = 0x1B8

-- Object structure offsets
local OBJECT_NAME = 0x78
local OBJECT_PARENT = 0x50
local OBJECT_CHILDREN = 0x80
local OBJECT_CHILDREN_END = 0x8
local OBJECT_CLASS_DESCRIPTOR = 0x18

-- Service offsets from DataModel
local WORKSPACE_OFFSET = 0x180
local PLAYERS_SERVICE_OFFSET = 0x80 -- DataModel + 0x80 + 0x0 + 0xF0
local PLAYERS_SERVICE_OFFSET2 = 0x0
local PLAYERS_SERVICE_OFFSET3 = 0xF0
local LOCAL_PLAYER_OFFSET = 0x128

-- Player character offsets
local CHARACTER_OFFSET = 0x1D0
local HUMANOID_OFFSET = 0x240
local ROOT_PART_OFFSET = 0x220

-- Property offsets
local GRAVITY_OFFSET = 0x8D0
local FOV_OFFSET = 0x168
local CAMERA_POS_OFFSET = 0x124
local CAMERA_ROTATION_OFFSET = 0x100
local CAMERA_SUBJECT_OFFSET = 0xF0
local CAMERA_TYPE_OFFSET = 0x160

-- Player properties
local USER_ID_OFFSET = 0x278
local TEAM_OFFSET = 0x258
local CHARACTER_APPEARANCE_ID_OFFSET = 0x268
local HEALTH_OFFSET = 0x19C
local MAX_HEALTH_OFFSET = 0x1BC
local WALK_SPEED_OFFSET = 0x1D8
local JUMP_POWER_OFFSET = 0x1B8

-- Workspace properties
local FOG_END_OFFSET = 0x138
local FOG_START_OFFSET = 0x13C
local FOG_COLOR_OFFSET = 0x104
local OUTDOOR_AMBIENT_OFFSET = 0x110

-- Camera properties
local CAMERA_OFFSET = 0x3F8
local CAMERA_MAX_ZOOM_DISTANCE_OFFSET = 0x2C0
local CAMERA_MIN_ZOOM_DISTANCE_OFFSET = 0x2C4
local CAMERA_MODE_OFFSET = 0x2C8

-- Part properties
local POSITION_OFFSET = 0x140
local ROTATION_OFFSET = 0x124
local PART_SIZE_OFFSET = 0x2B0
local TRANSPARENCY_OFFSET = 0xF8
local CAN_COLLIDE_OFFSET = 0x313
local ANCHORED_OFFSET = 0x311
local MATERIAL_TYPE_OFFSET = 0x2F0

-- String properties
local NAME_SIZE_OFFSET = 0x10
local STRING_LENGTH_OFFSET = 0x10

-- ========================================
-- GLOBAL VARIABLES
-- ========================================

local ui_state = {}
local font_handle = nil
local process_attached = false
local base_address = 0
local datamodel_address = 0
local workspace_address = 0
local players_service_address = 0
local local_player_address = 0
local connected_players = {}

-- ========================================
-- HELPER FUNCTIONS
-- ========================================

-- Helper function to read a Roblox string (with length prefix)
function read_roblox_string(address)
    if not address or address == 0 then
        return nil
    end

    -- Try different string formats
    -- Method 1: Length prefix (common format)
    local length = proc.read_int32(address)
    if length and length > 0 and length <= 1000 then
        local str = proc.read_string(address + 4, length)
        if str and str ~= "" then
            return str
        end
    end

    -- Method 2: Try reading as null-terminated string
    local str = proc.read_string(address, 100)
    if str and str ~= "" and #str > 0 then
        return str
    end

    -- Method 3: Try different length offset
    local length2 = proc.read_int16(address)
    if length2 and length2 > 0 and length2 <= 100 then
        local str = proc.read_string(address + 2, length2)
        if str and str ~= "" then
            return str
        end
    end

    return nil
end

-- Function to get object name
function get_object_name(object_address)
    if not object_address or object_address == 0 then
        return "Unknown"
    end

    -- Try reading name pointer
    local name_ptr = proc.read_int64(object_address + OBJECT_NAME)
    if not name_ptr or name_ptr == 0 then
        -- Try alternative: direct string at offset
        local direct_name = read_roblox_string(object_address + OBJECT_NAME)
        if direct_name then
            return direct_name
        end
        return "No Name Ptr"
    end

    local name = read_roblox_string(name_ptr)
    if name and name ~= "" then
        return name
    end

    -- Fallback: try reading with different offsets
    for i = 0, 32, 8 do
        local alt_name = read_roblox_string(name_ptr + i)
        if alt_name and alt_name ~= "" and #alt_name < 50 then
            return alt_name
        end
    end

    return "Failed to read name"
end

-- Function to get object class name
function get_object_class(object_address)
    if not object_address or object_address == 0 then
        return "Unknown"
    end

    local class_ptr = proc.read_int64(object_address + OBJECT_CLASS_DESCRIPTOR)
    if not class_ptr or class_ptr == 0 then
        return "No Class Ptr"
    end

    -- Try multiple common class descriptor layouts
    local class_offsets = { 0, 8, 16, 24, 32 } -- Common offsets for class name

    for _, offset in ipairs(class_offsets) do
        local class_name_ptr = proc.read_int64(class_ptr + offset)
        if class_name_ptr and class_name_ptr ~= 0 then
            local class_name = read_roblox_string(class_name_ptr)
            if class_name and class_name ~= "" and #class_name < 50 then
                return class_name
            end
        end
    end

    -- Try direct string read from class descriptor
    local direct_class = read_roblox_string(class_ptr)
    if direct_class and direct_class ~= "" then
        return direct_class
    end

    return "Failed to read class"
end

-- Function to find child by name
function find_child_by_name(parent_address, target_name)
    if not parent_address or parent_address == 0 then
        return 0
    end

    local children_start = proc.read_int64(parent_address + OBJECT_CHILDREN)
    local children_end = proc.read_int64(parent_address + OBJECT_CHILDREN_END)

    if not children_start or not children_end or children_start == 0 or children_end == 0 then
        return 0
    end

    -- Iterate through children (typically 8-byte pointers)
    local current = children_start
    while current < children_end do
        local child_address = proc.read_int64(current)
        if child_address and child_address ~= 0 then
            local child_name = get_object_name(child_address)
            if child_name == target_name then
                return child_address
            end
        end
        current = current + 8 -- Move to next pointer
    end

    return 0
end

-- Function to find service by class name
function find_service_by_class(datamodel_addr, target_class)
    if not datamodel_addr or datamodel_addr == 0 then
        return 0
    end

    local children_start = proc.read_int64(datamodel_addr + OBJECT_CHILDREN)
    local children_end = proc.read_int64(datamodel_addr + OBJECT_CHILDREN_END)

    if not children_start or not children_end or children_start == 0 or children_end == 0 then
        return 0
    end

    -- Iterate through children
    local current = children_start
    while current < children_end do
        local child_address = proc.read_int64(current)
        if child_address and child_address ~= 0 then
            local child_class = get_object_class(child_address)
            if child_class == target_class then
                return child_address
            end
        end
        current = current + 8
    end

    return 0
end

-- ========================================
-- DATAMODEL NAVIGATION
-- ========================================

function find_datamodel()
    if not process_attached then
        engine.log("Not attached to process!", 255, 0, 0, 255)
        return
    end

    engine.log("Finding DataModel using FakeDataModelPointer method...", 255, 255, 0, 255)
    engine.log("FakeDataModelPointer: " .. string.format("0x%X", FAKE_DATAMODEL_POINTER), 255, 255, 255, 255)
    engine.log("FakeDataModelToDataModel: " .. string.format("0x%X", FAKE_DATAMODEL_TO_DATAMODEL), 255, 255, 255, 255)

    -- Step 1: Read the FakeDataModel pointer (absolute address)
    engine.log("Step 1: Reading FakeDataModel pointer from absolute address...", 255, 255, 255, 255)
    local fake_datamodel = proc.read_int64(FAKE_DATAMODEL_POINTER)

    if not fake_datamodel or fake_datamodel == 0 then
        engine.log(
            "Failed to read FakeDataModel from absolute address: " .. string.format("0x%X", FAKE_DATAMODEL_POINTER), 255,
            0,
            0, 255)

        -- Try as offset from base address
        engine.log("Trying FakeDataModelPointer as offset from base address...", 255, 255, 0, 255)
        fake_datamodel = proc.read_int64(base_address + FAKE_DATAMODEL_POINTER)

        if not fake_datamodel or fake_datamodel == 0 then
            engine.log("Failed to read FakeDataModel as offset too!", 255, 0, 0, 255)
            return
        end
    end

    engine.log("FakeDataModel found at: " .. string.format("0x%X", fake_datamodel), 0, 255, 0, 255)

    -- Step 2: Read the actual DataModel using the offset
    engine.log("Step 2: Reading DataModel from FakeDataModel + offset...", 255, 255, 255, 255)
    datamodel_address = proc.read_int64(fake_datamodel + FAKE_DATAMODEL_TO_DATAMODEL)

    if not datamodel_address or datamodel_address == 0 then
        engine.log(
            "Failed to read DataModel from FakeDataModel + " .. string.format("0x%X", FAKE_DATAMODEL_TO_DATAMODEL), 255,
            0, 0,
            255)
        return
    end

    engine.log("DataModel found at: " .. string.format("0x%X", datamodel_address), 0, 255, 0, 255)

    -- Step 3: Verify we have a valid DataModel by reading its properties
    engine.log("Step 3: Verifying DataModel...", 255, 255, 255, 255)
    local dm_name = get_object_name(datamodel_address)
    local dm_class = get_object_class(datamodel_address)

    engine.log("DataModel Name: " .. dm_name, 255, 255, 255, 255)
    engine.log("DataModel Class: " .. dm_class, 255, 255, 255, 255)

    if datamodel_address > 0x100000 then
        engine.log("DataModel successfully found and verified!", 0, 255, 0, 255)
    else
        engine.log("Warning: DataModel address seems invalid!", 255, 255, 0, 255)
    end
end

function find_workspace()
    if datamodel_address == 0 then
        engine.log("DataModel not found! Find DataModel first.", 255, 0, 0, 255)
        return
    end

    engine.log("Reading Workspace using direct offset from DataModel...", 255, 255, 255, 255)
    workspace_address = proc.read_int64(datamodel_address + WORKSPACE_OFFSET)

    if workspace_address and workspace_address ~= 0 then
        engine.log("Workspace found at: " .. string.format("0x%X", workspace_address), 0, 255, 0, 255)
        local ws_name = get_object_name(workspace_address)
        local ws_class = get_object_class(workspace_address)
        engine.log("Workspace Name: " .. ws_name .. ", Class: " .. ws_class, 255, 255, 255, 255)
    else
        engine.log("Failed to read Workspace from DataModel + " .. string.format("0x%X", WORKSPACE_OFFSET), 255, 0, 0,
            255)
    end
end

function find_players_service()
    if datamodel_address == 0 then
        engine.log("DataModel not found! Find DataModel first.", 255, 0, 0, 255)
        return
    end

    engine.log("Reading Players service using direct offset path from DataModel...", 255, 255, 255, 255)
    engine.log("Path: DataModel + 0x80 + 0x0 + 0xF0", 255, 255, 255, 255)

    -- Step 1: DataModel + 0x80
    local step1 = proc.read_int64(datamodel_address + PLAYERS_SERVICE_OFFSET)
    if not step1 or step1 == 0 then
        engine.log("Failed at step 1: DataModel + 0x80", 255, 0, 0, 255)
        return
    end
    engine.log("Step 1 result: " .. string.format("0x%X", step1), 255, 255, 255, 255)

    -- Step 2: + 0x0 (should be same address)
    local step2 = proc.read_int64(step1 + PLAYERS_SERVICE_OFFSET2)
    if not step2 or step2 == 0 then
        engine.log("Failed at step 2: +0x0", 255, 0, 0, 255)
        return
    end
    engine.log("Step 2 result: " .. string.format("0x%X", step2), 255, 255, 255, 255)

    -- Step 3: + 0xF0
    players_service_address = proc.read_int64(step2 + PLAYERS_SERVICE_OFFSET3)

    if players_service_address and players_service_address ~= 0 then
        engine.log("Players service found at: " .. string.format("0x%X", players_service_address), 0, 255, 0, 255)
        local players_name = get_object_name(players_service_address)
        local players_class = get_object_class(players_service_address)
        engine.log("Players Service Name: " .. players_name .. ", Class: " .. players_class, 255, 255, 255, 255)
    else
        engine.log("Failed to read Players service from final offset!", 255, 0, 0, 255)
    end
end

function find_local_player()
    if players_service_address == 0 then
        engine.log("Players service not found! Find Players service first.", 255, 0, 0, 255)
        return
    end

    local_player_address = proc.read_int64(players_service_address + LOCAL_PLAYER_OFFSET)
    if local_player_address and local_player_address ~= 0 then
        engine.log("LocalPlayer found at: " .. string.format("0x%X", local_player_address), 0, 255, 0, 255)
        local player_name = get_object_name(local_player_address)
        engine.log("LocalPlayer Name: " .. player_name, 255, 255, 255, 255)
    else
        engine.log("Failed to find LocalPlayer!", 255, 0, 0, 255)
    end
end

-- ========================================
-- GUI INITIALIZATION
-- ========================================

function init_gui()
    engine.log("Initializing GUI...", 0, 255, 0, 255)

    local tab = gui.get_tab("visuals")
    ui_state.main_panel = tab:create_panel("Roblox DataModel Navigation", false)

    -- Process control
    ui_state.attach_button = ui_state.main_panel:add_button("Attach to Roblox", function()
        attach_to_roblox()
    end)

    ui_state.status_text = ui_state.main_panel:add_text("Status: Not attached")

    -- Navigation panel
    ui_state.nav_panel = tab:create_panel("DataModel Navigation", true)
    ui_state.find_datamodel_btn = ui_state.nav_panel:add_button("Find DataModel", function()
        find_datamodel()
    end)
    ui_state.find_workspace_btn = ui_state.nav_panel:add_button("Find Workspace", function()
        find_workspace()
    end)
    ui_state.find_players_btn = ui_state.nav_panel:add_button("Find Players Service", function()
        find_players_service()
    end)
    ui_state.find_localplayer_btn = ui_state.nav_panel:add_button("Find LocalPlayer", function()
        find_local_player()
    end)

    -- Memory reading panel
    ui_state.memory_panel = tab:create_panel("Memory Reading", true)
    ui_state.read_workspace_props = ui_state.memory_panel:add_button("Read Workspace Properties", function()
        read_workspace_properties()
    end)
    ui_state.read_player_props = ui_state.memory_panel:add_button("Read Player Properties", function()
        read_player_properties()
    end)
    ui_state.enumerate_players = ui_state.memory_panel:add_button("Enumerate Players", function()
        enumerate_connected_players()
    end)
    ui_state.get_player_names = ui_state.memory_panel:add_button("Get All Player Names", function()
        local names = get_all_player_names()
        if #names > 0 then
            ui_state.memory_output:set("Players: " .. table.concat(names, ", "))
        else
            ui_state.memory_output:set("No players found")
        end
    end)
    ui_state.debug_players = ui_state.memory_panel:add_button("Debug Player Memory", function()
        debug_player_memory()
    end)
    ui_state.search_username = ui_state.memory_panel:add_button("Search My Username", function()
        search_for_username()
    end)
    ui_state.dump_offsets = ui_state.memory_panel:add_button("Dump Players Offsets", function()
        dump_players_offsets()
    end)
    ui_state.memory_output = ui_state.memory_panel:add_text("Memory data will appear here")

    -- Features panel
    ui_state.features_panel = tab:create_panel("Features", true)
    ui_state.enable_esp = ui_state.features_panel:add_checkbox("Enable ESP")
    ui_state.enable_info = ui_state.features_panel:add_checkbox("Show Info Display")
    ui_state.esp_color = ui_state.features_panel:add_color_picker("ESP Color", 255, 0, 0, 255)
    ui_state.info_keybind = ui_state.features_panel:add_keybind("Info Toggle", 45, key_mode.toggle) -- INSERT key

    -- Set initial values
    ui_state.enable_esp:set(true)
    ui_state.enable_info:set(true)

    engine.log("GUI initialized successfully!", 0, 255, 0, 255)
end

-- ========================================
-- PROCESS MANAGEMENT
-- ========================================

function attach_to_roblox()
    engine.log("Attempting to attach to Roblox...", 255, 255, 0, 255)

    if proc.attach_by_name("RobloxPlayerBeta.exe") then
        process_attached = true
        base_address = proc.base_address()

        if base_address then
            ui_state.status_text:set("Status: Attached to Roblox (Base: " .. string.format("0x%X", base_address) .. ")")
            engine.log("Successfully attached to Roblox! Base: " .. string.format("0x%X", base_address), 0, 255, 0, 255)

            -- Automatically find all components after successful attachment
            engine.log("Finding DataModel, Workspace, Players service, and LocalPlayer...", 0, 255, 255, 255)
            find_datamodel()
            find_workspace()
            find_players_service()
            find_local_player()
        else
            ui_state.status_text:set("Status: Attached but couldn't get base address")
            engine.log("Attached but failed to get base address!", 255, 0, 0, 255)
        end
    else
        process_attached = false
        ui_state.status_text:set("Status: Failed to attach - Make sure Roblox is running")
        engine.log("Failed to attach to Roblox! Make sure RobloxPlayerBeta.exe is running.", 255, 0, 0, 255)
    end
end



-- ========================================
-- MEMORY READING FUNCTIONS
-- ========================================

function read_workspace_properties()
    if workspace_address == 0 then
        ui_state.memory_output:set("Workspace not found!")
        return
    end

    local info_parts = {}

    -- Read Gravity
    local gravity = proc.read_float(workspace_address + GRAVITY_OFFSET)
    if gravity then
        table.insert(info_parts, "Gravity: " .. string.format("%.1f", gravity))
    end

    -- Read FogEnd
    local fog_end = proc.read_float(workspace_address + FOG_END_OFFSET)
    if fog_end then
        table.insert(info_parts, "FogEnd: " .. string.format("%.1f", fog_end))
    end

    -- Read FogStart
    local fog_start = proc.read_float(workspace_address + FOG_START_OFFSET)
    if fog_start then
        table.insert(info_parts, "FogStart: " .. string.format("%.1f", fog_start))
    end

    local result = table.concat(info_parts, " | ")
    ui_state.memory_output:set("Workspace: " .. result)
    engine.log("Workspace Properties: " .. result, 255, 255, 255, 255)
end

function read_player_properties()
    if local_player_address == 0 then
        ui_state.memory_output:set("LocalPlayer not found!")
        return
    end

    local info_parts = {}

    -- Read UserId
    local userid = proc.read_int64(local_player_address + USER_ID_OFFSET)
    if userid then
        table.insert(info_parts, "UserId: " .. tostring(userid))
    end

    -- Read Team (if available)
    local team_ptr = proc.read_int64(local_player_address + TEAM_OFFSET)
    if team_ptr and team_ptr ~= 0 then
        local team_name = get_object_name(team_ptr)
        table.insert(info_parts, "Team: " .. team_name)
    else
        table.insert(info_parts, "Team: None")
    end

    local result = table.concat(info_parts, " | ")
    ui_state.memory_output:set("LocalPlayer: " .. result)
    engine.log("LocalPlayer Properties: " .. result, 255, 255, 255, 255)
end

-- Get all player names using systematic child array approach (based on test all player.lua)
function get_all_player_names()
    if players_service_address == 0 then
        engine.log("Error: Players service address is null.", 255, 0, 0, 255)
        return {}
    end

    local player_names = {}
    engine.log("Getting all player names using child array method...", 255, 255, 0, 255)

    -- 1. Get the address of the children management object from the Players service
    local child_list_object_address = proc.read_int64(players_service_address + OBJECT_CHILDREN)
    if not child_list_object_address or child_list_object_address == 0 then
        engine.log("Error: Could not read child list object address from Players service.", 255, 0, 0, 255)
        return player_names
    end

    engine.log("Child list object found at: " .. string.format("0x%X", child_list_object_address), 255, 255, 255, 255)

    -- 2. Get the pointers to the beginning and end of the child entries array
    local current_child_entry_address = proc.read_int64(child_list_object_address)
    local end_of_child_entries_address = proc.read_int64(child_list_object_address + OBJECT_CHILDREN_END)

    if not current_child_entry_address or not end_of_child_entries_address or
       current_child_entry_address == 0 or end_of_child_entries_address == 0 then
        engine.log("Error: Child array begin or end pointer is null. (Or no players)", 255, 0, 0, 255)
        return player_names
    end

    if current_child_entry_address == end_of_child_entries_address then
        engine.log("No players found (child array is empty).", 255, 255, 0, 255)
        return player_names
    end

    engine.log("Child array: " .. string.format("0x%X", current_child_entry_address) .. " to " .. string.format("0x%X", end_of_child_entries_address), 255, 255, 255, 255)

    -- 3. Iterate through the array of child entries
    local player_count = 0
    while current_child_entry_address < end_of_child_entries_address do
        -- Each entry is 8 bytes (pointer size)
        local player_instance_address = proc.read_int64(current_child_entry_address)

        if player_instance_address and player_instance_address ~= 0 then
            -- Get the player's name using our existing function
            local player_name = get_object_name(player_instance_address)
            local player_class = get_object_class(player_instance_address)

            if player_name and player_class == "Player" and
               player_name ~= "Failed to read name" and player_name ~= "No Name Ptr" then
                table.insert(player_names, player_name)
                player_count = player_count + 1
                engine.log("Found player: " .. player_name .. " at " .. string.format("0x%X", player_instance_address), 0, 255, 0, 255)
            end
        end

        -- Move to the next entry (8 bytes for 64-bit pointers)
        current_child_entry_address = current_child_entry_address + 8
    end

    engine.log("Player enumeration complete! Found " .. player_count .. " players", 0, 255, 0, 255)
    return player_names
end

-- Enumerate connected players using systematic approach
function enumerate_connected_players()
    if players_service_address == 0 then
        ui_state.memory_output:set("Players service not found!")
        return
    end

    engine.log("Enumerating connected players using systematic child array method...", 255, 255, 0, 255)
    connected_players = {}

    -- Use the systematic approach to get all player names
    local player_names = get_all_player_names()

    if #player_names > 0 then
        -- Convert names to player info objects for compatibility with existing code
        for _, name in ipairs(player_names) do
            local player_info = {
                name = name,
                method = "ChildArray"
            }
            table.insert(connected_players, player_info)
        end

        ui_state.memory_output:set("Found " .. #player_names .. " players: " .. table.concat(player_names, ", "))
        engine.log("Successfully found " .. #player_names .. " players using child array method", 0, 255, 0, 255)
    else
        -- Fallback to original scanning methods if systematic approach fails
        engine.log("Child array method failed, falling back to scanning methods...", 255, 255, 0, 255)

        -- Method 1: Direct player array scanning
        engine.log("Method 1: Looking for player array structures...", 255, 255, 255, 255)
        scan_for_player_arrays()

        -- Method 2: Scan around known LocalPlayer location
        engine.log("Method 2: Scanning around LocalPlayer location...", 255, 255, 255, 255)
        scan_around_localplayer()

        -- Method 3: Check for player list pointers
        engine.log("Method 3: Looking for player list pointers...", 255, 255, 255, 255)
        scan_for_player_lists()

        ui_state.memory_output:set("Found " .. #connected_players .. " players total (fallback methods)")
    end

    engine.log("Enumeration complete! Found " .. #connected_players .. " unique players", 0, 255, 0, 255)
end

-- Method 1: Look for player array structures
function scan_for_player_arrays()
    local found_addresses = {}

    -- Check common array pointer offsets that might contain player lists
    local array_offsets = {
        0x90, 0x98, 0xA0, 0xA8, 0xB0, 0xB8, 0xC0, 0xC8, 0xD0, 0xD8, 0xE0, 0xE8, 0xF0, 0xF8,
        0x100, 0x110, 0x130, 0x140, 0x150, 0x160, 0x170, 0x180, 0x190, 0x1A0, 0x1B0, 0x1C0
    }

    for _, offset in ipairs(array_offsets) do
        local success, array_ptr = pcall(proc.read_int64, players_service_address + offset)
        if success and array_ptr and array_ptr > 0x100000 and array_ptr < 0x7FFFFFFFFFFF then
            engine.log("Checking potential player array at offset " .. string.format("0x%X", offset) ..
                " -> " .. string.format("0x%X", array_ptr), 255, 255, 255, 255)

            -- Try reading as array of player pointers
            for i = 0, 20 do -- Check up to 20 players
                local player_success, player_addr = pcall(proc.read_int64, array_ptr + (i * 8))
                if player_success and player_addr and player_addr > 0x100000 and player_addr < 0x7FFFFFFFFFFF then
                    if not found_addresses[player_addr] then
                        found_addresses[player_addr] = true
                        engine.log("Checking array element " .. i .. " at " .. string.format("0x%X", player_addr), 255,
                            255, 255, 255)
                        if check_if_valid_player(player_addr, "Array-" .. string.format("0x%X", offset)) then
                            -- If we found a player, continue scanning this array
                        end
                    end
                else
                    if i > 0 then break end -- Stop if we hit invalid after finding some
                end
            end
        end
    end
end

-- Method 2: Scan around LocalPlayer location (we know it's at offset 0x128)
function scan_around_localplayer()
    if local_player_address == 0 then return end

    local found_addresses = {}

    -- We know LocalPlayer is at Players+0x128, so check nearby offsets for other players
    local player_offsets = {
        0x120, 0x130, 0x138, 0x148, 0x158, 0x168, 0x178, 0x188, 0x198, 0x1A8,
        0x1B8, 0x1C8, 0x1D8, 0x1E8, 0x1F8, 0x208, 0x218, 0x228, 0x238, 0x248
    }

    for _, offset in ipairs(player_offsets) do
        local success, potential_player = pcall(proc.read_int64, players_service_address + offset)
        if success and potential_player and potential_player > 0x100000 and potential_player < 0x7FFFFFFFFFFF then
            if potential_player ~= local_player_address and not found_addresses[potential_player] then
                found_addresses[potential_player] = true
                check_if_valid_player(potential_player, "LocalArea-" .. string.format("0x%X", offset))
            end
        end
    end

    -- Also try sequential scanning from LocalPlayer area
    for offset = -0x200, 0x200, 8 do
        if offset ~= 0 then -- Skip LocalPlayer itself
            local success, potential_player = pcall(proc.read_int64, local_player_address + offset)
            if success and potential_player and potential_player > 0x100000 and potential_player < 0x7FFFFFFFFFFF then
                if not found_addresses[potential_player] then
                    found_addresses[potential_player] = true
                    check_if_valid_player(potential_player, "Sequential")
                end
            end
        end
    end
end

-- Method 3: Look for player list pointers (vectors, arrays, etc.)
function scan_for_player_lists()
    local found_addresses = {}

    -- Look for C++ vector-like structures (start, end, capacity pointers)
    for offset = 0x80, 0x300, 8 do
        local ptr1_ok, ptr1 = pcall(proc.read_int64, players_service_address + offset)
        local ptr2_ok, ptr2 = pcall(proc.read_int64, players_service_address + offset + 8)
        local ptr3_ok, ptr3 = pcall(proc.read_int64, players_service_address + offset + 16)

        if ptr1_ok and ptr2_ok and ptr3_ok and ptr1 and ptr2 and ptr3 and
            ptr1 > 0x100000 and ptr2 > ptr1 and ptr3 >= ptr2 and
            (ptr2 - ptr1) % 8 == 0 and (ptr2 - ptr1) / 8 <= 50 then
            local count = math.floor((ptr2 - ptr1) / 8)
            engine.log("Found potential vector at offset " .. string.format("0x%X", offset) ..
                " with " .. count .. " elements", 255, 255, 255, 255)

            -- Read each element in the vector
            for i = 0, count - 1 do
                local elem_ok, elem_addr = pcall(proc.read_int64, ptr1 + (i * 8))
                if elem_ok and elem_addr and elem_addr > 0x100000 and elem_addr < 0x7FFFFFFFFFFF then
                    if not found_addresses[elem_addr] then
                        found_addresses[elem_addr] = true
                        engine.log("Checking vector element " .. i .. " at " .. string.format("0x%X", elem_addr), 255,
                            255, 255, 255)
                        check_if_valid_player(elem_addr, "Vector-" .. string.format("0x%X", offset))
                    end
                end
            end
        end
    end

    -- Also check for simple array structures with size prefix
    for offset = 0x80, 0x300, 8 do
        local size_ok, size = pcall(proc.read_int32, players_service_address + offset)
        if size_ok and size and size > 0 and size <= 50 then
            local array_ok, array_ptr = pcall(proc.read_int64, players_service_address + offset + 8)
            if array_ok and array_ptr and array_ptr > 0x100000 then
                engine.log("Found potential sized array at offset " .. string.format("0x%X", offset) ..
                    " with size " .. size, 255, 255, 255, 255)

                for i = 0, size - 1 do
                    local player_ok, player_addr = pcall(proc.read_int64, array_ptr + (i * 8))
                    if player_ok and player_addr and player_addr > 0x100000 and player_addr < 0x7FFFFFFFFFFF then
                        if not found_addresses[player_addr] then
                            found_addresses[player_addr] = true
                            engine.log("Checking array element " .. i .. " at " .. string.format("0x%X", player_addr),
                                255, 255, 255, 255)
                            check_if_valid_player(player_addr, "SizedArray-" .. string.format("0x%X", offset))
                        end
                    end
                end
            end
        end
    end
end

engine.log("=== DEBUG COMPLETE ===", 255, 255, 0, 255)

-- Search for player's username in memory to understand storage pattern
function search_for_username()
    if players_service_address == 0 then
        engine.log("Players service not found!", 255, 0, 0, 255)
        return
    end

    -- Try to get the LocalPlayer's name first
    local target_username = "Unknown"
    if local_player_address ~= 0 then
        target_username = get_object_name(local_player_address) or "Unknown"
    end

    engine.log("Searching for username '" .. target_username .. "' in Players service memory...", 255, 255, 0, 255)

    local found_locations = {}

    -- Search in a large range around Players service
    for offset = 0, 0x10000, 8 do
        local success, addr = pcall(proc.read_int64, players_service_address + offset)
        if success and addr and addr > 0x100000 and addr < 0x7FFFFFFFFFFF then
            -- Try reading as object name
            local name_ok, name = pcall(get_object_name, addr)
            if name_ok and name == target_username then
                local class_ok, class = pcall(get_object_class, addr)
                table.insert(found_locations, {
                    offset = offset,
                    address = addr,
                    name = name,
                    class = class_ok and class or "Unknown"
                })
                engine.log("Found '" .. target_username .. "' at offset " .. string.format("0x%X", offset) ..
                    " -> " .. string.format("0x%X", addr) .. " (Class: " .. tostring(class) .. ")", 0, 255, 0, 255)
            end
        end
    end

    -- Also search for the username string directly in memory
    engine.log("Searching for username string pattern in memory...", 255, 255, 255, 255)
    for offset = 0, 0x5000, 4 do
        local success, str = pcall(proc.read_string, players_service_address + offset, 50)
        if success and str and str:find(target_username) then
            engine.log("Found username string at offset " .. string.format("0x%X", offset) .. ": " .. str, 0, 255, 255,
                255)
        end
    end

    if #found_locations > 0 then
        engine.log("Analysis of found locations:", 255, 255, 0, 255)
        for i, loc in ipairs(found_locations) do
            -- Check what's around this location
            engine.log("Location " .. i .. " - Checking nearby memory...", 255, 255, 255, 255)

            -- Check if this looks like it's in an array
            local prev_addr = proc.read_int64(players_service_address + loc.offset - 8)
            local next_addr = proc.read_int64(players_service_address + loc.offset + 8)

            if prev_addr and prev_addr > 0x100000 then
                local prev_name = get_object_name(prev_addr)
                engine.log("  Previous slot: " .. tostring(prev_name), 255, 255, 255, 255)
            end

            if next_addr and next_addr > 0x100000 then
                local next_name = get_object_name(next_addr)
                engine.log("  Next slot: " .. tostring(next_name), 255, 255, 255, 255)
            end

            -- Check the address itself for UserId
            local userid_ok, userid = pcall(proc.read_int64, loc.address + USER_ID_OFFSET)
            if userid_ok and userid then
                engine.log("  UserId at this location: " .. tostring(userid), 255, 255, 255, 255)
            end
        end
    else
        engine.log("Username not found in scanned range!", 255, 0, 0, 255)
    end
end

-- Debug function to analyze player memory structure
function debug_player_memory()
    if players_service_address == 0 then
        engine.log("Players service not found!", 255, 0, 0, 255)
        return
    end

    engine.log("=== DEBUGGING PLAYER MEMORY STRUCTURE ===", 255, 255, 0, 255)

    -- Debug Players service structure
    engine.log("Players Service Address: " .. string.format("0x%X", players_service_address), 255, 255, 255, 255)

    -- Check if Players service has children structure
    local children_ok, children_start = pcall(proc.read_int64, players_service_address + OBJECT_CHILDREN)
    local children_end_ok, children_end = pcall(proc.read_int64, players_service_address + OBJECT_CHILDREN_END)

    if children_ok and children_end_ok and children_start and children_end then
        engine.log("Children Start: " .. string.format("0x%X", children_start), 255, 255, 255, 255)
        engine.log("Children End: " .. string.format("0x%X", children_end), 255, 255, 255, 255)
        engine.log("Children Count: " .. math.floor((children_end - children_start) / 8), 255, 255, 255, 255)
    end

    -- Debug LocalPlayer structure if available
    if local_player_address ~= 0 then
        engine.log("LocalPlayer Address: " .. string.format("0x%X", local_player_address), 255, 255, 255, 255)

        local parent_ok, parent = pcall(proc.read_int64, local_player_address + OBJECT_PARENT)
        if parent_ok and parent then
            engine.log("LocalPlayer Parent: " .. string.format("0x%X", parent), 255, 255, 255, 255)

            if parent == players_service_address then
                engine.log("LocalPlayer parent IS the Players service!", 0, 255, 0, 255)
            else
                engine.log("LocalPlayer parent is NOT the Players service", 255, 255, 0, 255)
                local parent_name = get_object_name(parent)
                engine.log("Parent name: " .. tostring(parent_name), 255, 255, 255, 255)
            end
        end
    end

    -- Look for potential player arrays by examining larger memory ranges
    engine.log("Scanning for player-like objects in larger range...", 255, 255, 0, 255)
    local potential_players = {}

    for offset = 0, 0x10000, 8 do
        local success, addr = pcall(proc.read_int64, players_service_address + offset)
        if success and addr and addr > 0x100000 and addr < 0x7FFFFFFFFFFF then
            local name_ok, name = pcall(get_object_name, addr)
            local class_ok, class = pcall(get_object_class, addr)

            if name_ok and class_ok and name and class and
                name ~= "Failed to read name" and class ~= "Failed to read class" and
                name ~= "No Name Ptr" and class ~= "No Class Ptr" then
                local entry = {
                    offset = offset,
                    address = addr,
                    name = name,
                    class = class
                }

                if class == "Player" then
                    entry.is_player = true
                    local userid_ok, userid = pcall(proc.read_int64, addr + USER_ID_OFFSET)
                    if userid_ok and userid then
                        entry.userid = userid
                    end
                end

                table.insert(potential_players, entry)
            end
        end
    end

    engine.log("Found " .. #potential_players .. " potential objects:", 255, 255, 255, 255)
    for i, obj in ipairs(potential_players) do
        if i > 20 then
            engine.log("... and " .. (#potential_players - 20) .. " more objects", 255, 255, 255, 255)
            break
        end

        local info = string.format("Offset 0x%X: %s (%s)", obj.offset, obj.name, obj.class)
        if obj.is_player and obj.userid then
            info = info .. " - Player ID: " .. obj.userid
            engine.log(info, 0, 255, 0, 255)
        else
            engine.log(info, 255, 255, 255, 255)
        end
    end

    engine.log("=== DEBUG COMPLETE ===", 255, 255, 0, 255)
end

-- Check if an address points to a valid player object
function check_if_valid_player(player_address, method)
    -- Check if we already found this player
    for _, existing_player in ipairs(connected_players) do
        if existing_player.address == player_address then
            return false
        end
    end

    local name_ok, player_name = pcall(get_object_name, player_address)
    local class_ok, player_class = pcall(get_object_class, player_address)

    engine.log(
    "  -> Checking " ..
    string.format("0x%X", player_address) ..
    " - Name: " .. tostring(player_name) .. ", Class: " .. tostring(player_class), 255, 255, 255, 255)

    -- Validate it's actually a player
    if name_ok and class_ok and player_name and player_class then
        if player_class == "Player" and
            player_name ~= "Players" and
            player_name ~= "StarterPlayers" and
            player_name ~= "PlayerGui" and
            player_name ~= "PlayerScripts" and
            player_name ~= "Failed to read name" and
            player_name ~= "No Name Ptr" then
            engine.log("  -> Valid player class and name, checking UserId...", 255, 255, 255, 255)

            -- Verify with UserId
            local userid_ok, userid = pcall(proc.read_int64, player_address + USER_ID_OFFSET)
            engine.log("  -> UserId read: " .. tostring(userid_ok) .. ", Value: " .. tostring(userid), 255, 255, 255, 255)

            if userid_ok and userid and userid > 0 and userid < 9999999999 then
                local player_info = {
                    address = player_address,
                    name = player_name,
                    class = player_class,
                    userid = userid,
                    method = method
                }

                -- Try to get character info
                local char_ok, character_ptr = pcall(proc.read_int64, player_address + CHARACTER_OFFSET)
                if char_ok and character_ptr and character_ptr ~= 0 then
                    player_info.character_address = character_ptr
                    local char_name_ok, character_name = pcall(get_object_name, character_ptr)
                    if char_name_ok and character_name then
                        player_info.character_name = character_name
                    end

                    -- Try to get position
                    local root_ok, root_part = pcall(proc.read_int64, character_ptr + ROOT_PART_OFFSET)
                    if root_ok and root_part and root_part ~= 0 then
                        local pos_x_ok, pos_x = pcall(proc.read_float, root_part + POSITION_OFFSET)
                        local pos_y_ok, pos_y = pcall(proc.read_float, root_part + POSITION_OFFSET + 4)
                        local pos_z_ok, pos_z = pcall(proc.read_float, root_part + POSITION_OFFSET + 8)

                        if pos_x_ok and pos_y_ok and pos_z_ok and pos_x and pos_y and pos_z then
                            player_info.position = { x = pos_x, y = pos_y, z = pos_z }
                        end
                    end
                end

                table.insert(connected_players, player_info)
                engine.log(
                    "Found Player [" .. method .. "]: " .. player_name .. " (ID: " .. userid .. ")", 0, 255, 0, 255)
                return true
            else
                engine.log("  -> Invalid UserId or read failed", 255, 0, 0, 255)
            end
        else
            engine.log("  -> Not a valid player: class=" .. tostring(player_class) .. ", name=" .. tostring(player_name),
                255, 0, 0, 255)
        end
    else
        engine.log("  -> Failed to read name or class", 255, 0, 0, 255)
    end
    return false
end

-- Dump all offsets from Players service for analysis
function dump_players_offsets()
    if players_service_address == 0 then
        ui_state.memory_output:set("Players service not found!")
        return
    end

    engine.log("Dumping Players service offsets 0x0 to 0xFFF...", 255, 255, 0, 255)

    local dump_data = {
        base_address = string.format("0x%X", players_service_address),
        timestamp = time.now_local(),
        offsets = {}
    }

    local valid_offsets = 0

    for offset = 0, 0xFFF, 8 do -- Step by 8 bytes (pointer size)
        local success, value = pcall(proc.read_int64, players_service_address + offset)
        if success and value and value ~= 0 then
            local offset_info = {
                offset = string.format("0x%X", offset),
                value = string.format("0x%X", value),
                decimal_value = value
            }

            -- Try to read as string if it looks like a valid pointer (with error handling)
            if value > 0x100000 and value < 0x7FFFFFFFFFFF then
                local string_ok, potential_string = pcall(read_roblox_string, value)
                if string_ok and potential_string and potential_string ~= "" and #potential_string < 100 then
                    offset_info.string_value = potential_string
                end

                -- Try to read object name (with error handling)
                local name_ok, potential_name = pcall(get_object_name, value)
                if name_ok and potential_name and potential_name ~= "Failed to read name" and potential_name ~= "No Name Ptr" then
                    offset_info.object_name = potential_name
                end

                -- Try to read object class (with error handling)
                local class_ok, potential_class = pcall(get_object_class, value)
                if class_ok and potential_class and potential_class ~= "Failed to read class" and potential_class ~= "No Class Ptr" then
                    offset_info.object_class = potential_class
                end
            end

            -- Also try reading as different data types (with error handling)
            local float_ok, float_val = pcall(proc.read_float, players_service_address + offset)
            if float_ok and float_val and float_val ~= 0 and float_val > -1000000 and float_val < 1000000 then
                offset_info.float_value = float_val
            end

            local int32_ok, int32_val = pcall(proc.read_int32, players_service_address + offset)
            if int32_ok and int32_val and int32_val ~= 0 then
                offset_info.int32_value = int32_val
            end

            table.insert(dump_data.offsets, offset_info)
            valid_offsets = valid_offsets + 1
        end
    end

    -- Save to JSON file (with error handling)
    local json_ok, json_output = pcall(json.stringify, dump_data)
    if not json_ok then
        ui_state.memory_output:set("Failed to create JSON data!")
        engine.log("JSON serialization failed!", 255, 0, 0, 255)
        return
    end

    local filename = "players_offset_dump_" .. tostring(time.unix()) .. ".json"
    local file_ok, file_error = pcall(fs.write_to_file, filename, json_output)

    if file_ok then
        ui_state.memory_output:set("Offset dump saved to " .. filename)
        engine.log("Players offset dump saved to: " .. filename, 0, 255, 0, 255)
        engine.log("Found " .. valid_offsets .. " non-zero offsets", 255, 255, 255, 255)
    else
        ui_state.memory_output:set("Failed to save file!")
        engine.log("File write failed: " .. tostring(file_error), 255, 0, 0, 255)
    end
end

-- ========================================
-- RENDERING FUNCTIONS
-- ========================================

function init_rendering()
    font_handle = render.create_font("arial.ttf", 16)
    if not font_handle then
        font_handle = render.create_font("C:/Windows/Fonts/arial.ttf", 16)
    end

    if font_handle then
        engine.log("Font created successfully!", 0, 255, 0, 255)
    else
        engine.log("Failed to create font, text rendering may not work", 255, 255, 0, 255)
    end
end

function render_overlay()
    if not ui_state.enable_esp or not ui_state.enable_esp:get() then
        return
    end

    local screen_width, screen_height = render.get_viewport_size()
    local box_size = 100
    local center_x = screen_width / 2 - box_size / 2
    local center_y = screen_height / 2 - box_size / 2

    local r, g, b, a = ui_state.esp_color:get()

    -- Draw ESP rectangle
    render.draw_rectangle(center_x, center_y, box_size, box_size, r, g, b, a, 2, false, 0)

    -- Draw corner indicators
    local corner_size = 10
    render.draw_line(center_x, center_y, center_x + corner_size, center_y, r, g, b, a, 2)
    render.draw_line(center_x, center_y, center_x, center_y + corner_size, r, g, b, a, 2)

    render.draw_line(center_x + box_size, center_y, center_x + box_size - corner_size, center_y, r, g, b, a, 2)
    render.draw_line(center_x + box_size, center_y, center_x + box_size, center_y + corner_size, r, g, b, a, 2)

    render.draw_line(center_x, center_y + box_size, center_x + corner_size, center_y + box_size, r, g, b, a, 2)
    render.draw_line(center_x, center_y + box_size, center_x, center_y + box_size - corner_size, r, g, b, a, 2)

    render.draw_line(center_x + box_size, center_y + box_size, center_x + box_size - corner_size, center_y + box_size, r,
        g, b, a, 2)
    render.draw_line(center_x + box_size, center_y + box_size, center_x + box_size, center_y + box_size - corner_size, r,
        g, b, a, 2)

    -- Draw center dot
    render.draw_circle(screen_width / 2, screen_height / 2, 3, 255, 255, 255, 255, 1, true)
end

function render_info_display()
    if not ui_state.enable_info or not ui_state.enable_info:get() then
        return
    end

    if not ui_state.info_keybind:is_active() then
        return
    end

    if not font_handle then
        return
    end

    local info_x = 10
    local info_y = 400
    local line_height = 20
    local current_y = info_y

    -- Background
    render.draw_rectangle(info_x - 5, info_y - 5, 400, 200, 0, 0, 0, 150, 1, true, 5)

    -- Title
    render.draw_text(font_handle, "Roblox DataModel Navigation Info", info_x, current_y, 255, 255, 255, 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- Process status
    local status_text = process_attached and "Process: Attached" or "Process: Not Attached"
    local status_color = process_attached and { 0, 255, 0 } or { 255, 0, 0 }
    render.draw_text(font_handle, status_text, info_x, current_y, status_color[1], status_color[2], status_color[3], 255,
        1, 0, 0, 0, 255)
    current_y = current_y + line_height

    -- DataModel status
    local dm_status = datamodel_address ~= 0 and ("DataModel: " .. string.format("0x%X", datamodel_address)) or
        "DataModel: Not Found"
    local dm_color = datamodel_address ~= 0 and { 0, 255, 0 } or { 255, 255, 0 }
    render.draw_text(font_handle, dm_status, info_x, current_y, dm_color[1], dm_color[2], dm_color[3], 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- Workspace status
    local ws_status = workspace_address ~= 0 and ("Workspace: " .. string.format("0x%X", workspace_address)) or
        "Workspace: Not Found"
    local ws_color = workspace_address ~= 0 and { 0, 255, 0 } or { 255, 255, 0 }
    render.draw_text(font_handle, ws_status, info_x, current_y, ws_color[1], ws_color[2], ws_color[3], 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- Players status
    local ps_status = players_service_address ~= 0 and ("Players: " .. string.format("0x%X", players_service_address)) or
        "Players: Not Found"
    local ps_color = players_service_address ~= 0 and { 0, 255, 0 } or { 255, 255, 0 }
    render.draw_text(font_handle, ps_status, info_x, current_y, ps_color[1], ps_color[2], ps_color[3], 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- LocalPlayer status
    local lp_status = local_player_address ~= 0 and ("LocalPlayer: " .. string.format("0x%X", local_player_address)) or
        "LocalPlayer: Not Found"
    local lp_color = local_player_address ~= 0 and { 0, 255, 0 } or { 255, 255, 0 }
    render.draw_text(font_handle, lp_status, info_x, current_y, lp_color[1], lp_color[2], lp_color[3], 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- FPS
    -- local fps = render.get_fps()
    -- render.draw_text(font_handle, "Overlay FPS: " .. tostring(fps), info_x, current_y, 255, 255, 255, 255, 1, 0, 0, 0,
    --     255)
    -- current_y = current_y + line_height

    -- Controls info
    render.draw_text(font_handle, "Controls: \nINSERT=Toggle Info, \nF2=Workspace Props, \nF3=Player Props, \nF4=Players",
        info_x,
        current_y,
        200, 200, 200, 255, 1, 0, 0, 0, 255)
end

-- Display connected players list
function render_players_display()
    if not ui_state.enable_info or not ui_state.enable_info:get() then
        return
    end

    if not ui_state.info_keybind:is_active() then
        return
    end

    if not font_handle or #connected_players == 0 then
        return
    end

    local screen_width, screen_height = render.get_viewport_size()
    local players_x = screen_width - 420
    local players_y = 10
    local line_height = 18
    local current_y = players_y

    -- Background for players list
    local bg_height = math.min(#connected_players * line_height + 40, screen_height - 20)
    render.draw_rectangle(players_x - 5, players_y - 5, 410, bg_height, 0, 0, 0, 150, 1, true, 5)

    -- Title
    render.draw_text(font_handle, "Connected Players (" .. #connected_players .. ")", players_x, current_y, 255, 255, 0,
        255, 1, 0, 0, 0, 255)
    current_y = current_y + line_height + 5

    -- Player list
    for i, player in ipairs(connected_players) do
        if current_y > screen_height - 40 then
            render.draw_text(font_handle, "... and " .. (#connected_players - i + 1) .. " more", players_x, current_y,
                200, 200, 200, 255, 1, 0, 0, 0, 255)
            break
        end

        local player_text = player.name or "Unknown"
        if player.userid then
            player_text = player_text .. " (ID: " .. player.userid .. ")"
        end

        -- Player name and ID
        render.draw_text(font_handle, player_text, players_x, current_y, 255, 255, 255, 255, 1, 0, 0, 0, 255)
        current_y = current_y + line_height

        -- Position if available
        if player.position then
            local pos_text = string.format("  Pos: (%.1f, %.1f, %.1f)", player.position.x, player.position.y,
                player.position.z)
            render.draw_text(font_handle, pos_text, players_x, current_y, 0, 255, 255, 255, 1, 0, 0, 0, 255)
            current_y = current_y + line_height
        end
    end
end

-- ========================================
-- MAIN LOOP AND EVENT HANDLING
-- ========================================

function on_engine_tick()
    -- Check if process is still alive
    if process_attached and proc.did_exit() then
        process_attached = false
        base_address = 0
        datamodel_address = 0
        workspace_address = 0
        players_service_address = 0
        local_player_address = 0
        ui_state.status_text:set("Status: Process exited")
        engine.log("Roblox process exited!", 255, 255, 0, 255)
    end

    -- Render overlay elements
    render_overlay()
    render_info_display()
    render_players_display()

    -- Hotkey handling
    if input.is_key_pressed(113) then -- F2 key
        read_workspace_properties()
    end

    if input.is_key_pressed(114) then -- F3 key
        read_player_properties()
    end

    if input.is_key_pressed(115) then -- F4 key
        enumerate_connected_players()
    end

    if input.is_key_pressed(116) then -- F5 key
        local names = get_all_player_names()
        if #names > 0 then
            engine.log("Players found: " .. table.concat(names, ", "), 0, 255, 0, 255)
        else
            engine.log("No players found", 255, 255, 0, 255)
        end
    end
end

function on_unload()
    engine.log("Roblox DataModel Navigation Script unloading...", 255, 255, 0, 255)
    engine.log("Script unloaded successfully!", 0, 255, 0, 255)
end

-- ========================================
-- INITIALIZATION
-- ========================================

function initialize_script()
    engine.log("Starting Roblox DataModel Navigation Script...", 0, 255, 255, 255)

    init_gui()
    init_rendering()

    engine.register_on_engine_tick(on_engine_tick)
    engine.register_onunload(on_unload)

    engine.log("Script initialized! Use 'Attach to Roblox' to automatically find all components", 0, 255,
        0, 255)
    engine.log("Hotkeys: F2=Workspace Props, F3=Player Props, F4=Enumerate Players, F5=Get Player Names, INSERT=Toggle Info", 0, 255, 0, 255)
end

-- Start the script
initialize_script()
