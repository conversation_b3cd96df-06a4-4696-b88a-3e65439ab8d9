-- Roblox DataModel Navigation Script
-- Demonstrates proper Roblox memory hierarchy navigation with hardcoded offsets

-- ========================================
-- HARDCODED OFFSETS AND POINTERS
-- ========================================

-- Core pointers (absolute addresses)
local FAKE_DATAMODEL_POINTER = 0x66EA5E8
local VISUAL_ENGINE_POINTER = 0x6535DD8
local TASK_SCHEDULER_POINTER = 0x67AB9E8
local JOBS_POINTER = 0x67ABBC0
local PLAYER_CONFIGURER_POINTER = 0x66C9D58

-- DataModel navigation offsets
local FAKE_DATAMODEL_TO_DATAMODEL = 0x1B8
local VISUAL_ENGINE_TO_DATAMODEL1 = 0x720
local VISUAL_ENGINE_TO_DATAMODEL2 = 0x1B8

-- Object structure offsets
local OBJECT_NAME = 0x78
local OBJECT_PARENT = 0x50
local OBJECT_CHILDREN = 0x80
local OBJECT_CHILDREN_END = 0x8
local OBJECT_CLASS_DESCRIPTOR = 0x18

-- Service offsets from DataModel
local WORKSPACE_OFFSET = 0x180
local PLAYERS_SERVICE_OFFSET = 0x80 -- DataModel + 0x80 + 0x0 + 0xF0
local PLAYERS_SERVICE_OFFSET2 = 0x0
local PLAYERS_SERVICE_OFFSET3 = 0xF0
local LOCAL_PLAYER_OFFSET = 0x128

-- Player character offsets
local CHARACTER_OFFSET = 0x1D0
local HUMANOID_OFFSET = 0x240
local ROOT_PART_OFFSET = 0x220

-- Property offsets
local GRAVITY_OFFSET = 0x8D0
local FOV_OFFSET = 0x168
local CAMERA_POS_OFFSET = 0x124
local CAMERA_ROTATION_OFFSET = 0x100
local CAMERA_SUBJECT_OFFSET = 0xF0
local CAMERA_TYPE_OFFSET = 0x160

-- Player properties
local USER_ID_OFFSET = 0x278
local TEAM_OFFSET = 0x258
local CHARACTER_APPEARANCE_ID_OFFSET = 0x268
local HEALTH_OFFSET = 0x19C
local MAX_HEALTH_OFFSET = 0x1BC
local WALK_SPEED_OFFSET = 0x1D8
local JUMP_POWER_OFFSET = 0x1B8

-- Workspace properties
local FOG_END_OFFSET = 0x138
local FOG_START_OFFSET = 0x13C
local FOG_COLOR_OFFSET = 0x104
local OUTDOOR_AMBIENT_OFFSET = 0x110

-- Camera properties
local CAMERA_OFFSET = 0x3F8
local CAMERA_MAX_ZOOM_DISTANCE_OFFSET = 0x2C0
local CAMERA_MIN_ZOOM_DISTANCE_OFFSET = 0x2C4
local CAMERA_MODE_OFFSET = 0x2C8

-- Part properties
local POSITION_OFFSET = 0x140
local ROTATION_OFFSET = 0x124
local PART_SIZE_OFFSET = 0x2B0
local TRANSPARENCY_OFFSET = 0xF8
local CAN_COLLIDE_OFFSET = 0x313
local ANCHORED_OFFSET = 0x311
local MATERIAL_TYPE_OFFSET = 0x2F0

-- String properties
local NAME_SIZE_OFFSET = 0x10
local STRING_LENGTH_OFFSET = 0x10

-- ========================================
-- GLOBAL VARIABLES
-- ========================================

local ui_state = {}
local font_handle = nil
local process_attached = false
local base_address = 0
local datamodel_address = 0
local workspace_address = 0
local players_service_address = 0
local local_player_address = 0
local connected_players = {}

-- ========================================
-- HELPER FUNCTIONS
-- ========================================

-- Helper function to read a Roblox string (with length prefix)
function read_roblox_string(address)
    if not address or address == 0 then
        return nil
    end

    -- Try different string formats
    -- Method 1: Length prefix (common format)
    local length = proc.read_int32(address)
    if length and length > 0 and length <= 1000 then
        local str = proc.read_string(address + 4, length)
        if str and str ~= "" then
            return str
        end
    end

    -- Method 2: Try reading as null-terminated string
    local str = proc.read_string(address, 100)
    if str and str ~= "" and #str > 0 then
        return str
    end

    -- Method 3: Try different length offset
    local length2 = proc.read_int16(address)
    if length2 and length2 > 0 and length2 <= 100 then
        local str = proc.read_string(address + 2, length2)
        if str and str ~= "" then
            return str
        end
    end

    return nil
end

-- Function to get object name
function get_object_name(object_address)
    if not object_address or object_address == 0 then
        return "Unknown"
    end

    -- Try reading name pointer
    local name_ptr = proc.read_int64(object_address + OBJECT_NAME)
    if not name_ptr or name_ptr == 0 then
        -- Try alternative: direct string at offset
        local direct_name = read_roblox_string(object_address + OBJECT_NAME)
        if direct_name then
            return direct_name
        end
        return "No Name Ptr"
    end

    local name = read_roblox_string(name_ptr)
    if name and name ~= "" then
        return name
    end

    -- Fallback: try reading with different offsets
    for i = 0, 32, 8 do
        local alt_name = read_roblox_string(name_ptr + i)
        if alt_name and alt_name ~= "" and #alt_name < 50 then
            return alt_name
        end
    end

    return "Failed to read name"
end

-- Function to get object class name
function get_object_class(object_address)
    if not object_address or object_address == 0 then
        return "Unknown"
    end

    local class_ptr = proc.read_int64(object_address + OBJECT_CLASS_DESCRIPTOR)
    if not class_ptr or class_ptr == 0 then
        return "No Class Ptr"
    end

    -- Try multiple common class descriptor layouts
    local class_offsets = { 0, 8, 16, 24, 32 } -- Common offsets for class name

    for _, offset in ipairs(class_offsets) do
        local class_name_ptr = proc.read_int64(class_ptr + offset)
        if class_name_ptr and class_name_ptr ~= 0 then
            local class_name = read_roblox_string(class_name_ptr)
            if class_name and class_name ~= "" and #class_name < 50 then
                return class_name
            end
        end
    end

    -- Try direct string read from class descriptor
    local direct_class = read_roblox_string(class_ptr)
    if direct_class and direct_class ~= "" then
        return direct_class
    end

    return "Failed to read class"
end

-- Function to find child by name
function find_child_by_name(parent_address, target_name)
    if not parent_address or parent_address == 0 then
        return 0
    end

    local children_start = proc.read_int64(parent_address + OBJECT_CHILDREN)
    local children_end = proc.read_int64(parent_address + OBJECT_CHILDREN_END)

    if not children_start or not children_end or children_start == 0 or children_end == 0 then
        return 0
    end

    -- Iterate through children (typically 8-byte pointers)
    local current = children_start
    while current < children_end do
        local child_address = proc.read_int64(current)
        if child_address and child_address ~= 0 then
            local child_name = get_object_name(child_address)
            if child_name == target_name then
                return child_address
            end
        end
        current = current + 8 -- Move to next pointer
    end

    return 0
end

-- Function to find service by class name
function find_service_by_class(datamodel_addr, target_class)
    if not datamodel_addr or datamodel_addr == 0 then
        return 0
    end

    local children_start = proc.read_int64(datamodel_addr + OBJECT_CHILDREN)
    local children_end = proc.read_int64(datamodel_addr + OBJECT_CHILDREN_END)

    if not children_start or not children_end or children_start == 0 or children_end == 0 then
        return 0
    end

    -- Iterate through children
    local current = children_start
    while current < children_end do
        local child_address = proc.read_int64(current)
        if child_address and child_address ~= 0 then
            local child_class = get_object_class(child_address)
            if child_class == target_class then
                return child_address
            end
        end
        current = current + 8
    end

    return 0
end

-- ========================================
-- DATAMODEL NAVIGATION
-- ========================================

function find_datamodel()
    if not process_attached then
        engine.log("Not attached to process!", 255, 0, 0, 255)
        return
    end

    engine.log("Finding DataModel using FakeDataModelPointer method...", 255, 255, 0, 255)
    engine.log("FakeDataModelPointer: " .. string.format("0x%X", FAKE_DATAMODEL_POINTER), 255, 255, 255, 255)
    engine.log("FakeDataModelToDataModel: " .. string.format("0x%X", FAKE_DATAMODEL_TO_DATAMODEL), 255, 255, 255, 255)

    -- Step 1: Read the FakeDataModel pointer (absolute address)
    engine.log("Step 1: Reading FakeDataModel pointer from absolute address...", 255, 255, 255, 255)
    local fake_datamodel = proc.read_int64(FAKE_DATAMODEL_POINTER)

    if not fake_datamodel or fake_datamodel == 0 then
        engine.log(
            "Failed to read FakeDataModel from absolute address: " .. string.format("0x%X", FAKE_DATAMODEL_POINTER), 255,
            0,
            0, 255)

        -- Try as offset from base address
        engine.log("Trying FakeDataModelPointer as offset from base address...", 255, 255, 0, 255)
        fake_datamodel = proc.read_int64(base_address + FAKE_DATAMODEL_POINTER)

        if not fake_datamodel or fake_datamodel == 0 then
            engine.log("Failed to read FakeDataModel as offset too!", 255, 0, 0, 255)
            return
        end
    end

    engine.log("FakeDataModel found at: " .. string.format("0x%X", fake_datamodel), 0, 255, 0, 255)

    -- Step 2: Read the actual DataModel using the offset
    engine.log("Step 2: Reading DataModel from FakeDataModel + offset...", 255, 255, 255, 255)
    datamodel_address = proc.read_int64(fake_datamodel + FAKE_DATAMODEL_TO_DATAMODEL)

    if not datamodel_address or datamodel_address == 0 then
        engine.log(
            "Failed to read DataModel from FakeDataModel + " .. string.format("0x%X", FAKE_DATAMODEL_TO_DATAMODEL), 255,
            0, 0,
            255)
        return
    end

    engine.log("DataModel found at: " .. string.format("0x%X", datamodel_address), 0, 255, 0, 255)

    -- Step 3: Verify we have a valid DataModel by reading its properties
    engine.log("Step 3: Verifying DataModel...", 255, 255, 255, 255)
    local dm_name = get_object_name(datamodel_address)
    local dm_class = get_object_class(datamodel_address)

    engine.log("DataModel Name: " .. dm_name, 255, 255, 255, 255)
    engine.log("DataModel Class: " .. dm_class, 255, 255, 255, 255)

    if datamodel_address > 0x100000 then
        engine.log("DataModel successfully found and verified!", 0, 255, 0, 255)
    else
        engine.log("Warning: DataModel address seems invalid!", 255, 255, 0, 255)
    end
end

function find_workspace()
    if datamodel_address == 0 then
        engine.log("DataModel not found! Find DataModel first.", 255, 0, 0, 255)
        return
    end

    engine.log("Reading Workspace using direct offset from DataModel...", 255, 255, 255, 255)
    workspace_address = proc.read_int64(datamodel_address + WORKSPACE_OFFSET)

    if workspace_address and workspace_address ~= 0 then
        engine.log("Workspace found at: " .. string.format("0x%X", workspace_address), 0, 255, 0, 255)
        local ws_name = get_object_name(workspace_address)
        local ws_class = get_object_class(workspace_address)
        engine.log("Workspace Name: " .. ws_name .. ", Class: " .. ws_class, 255, 255, 255, 255)
    else
        engine.log("Failed to read Workspace from DataModel + " .. string.format("0x%X", WORKSPACE_OFFSET), 255, 0, 0,
            255)
    end
end

function find_players_service()
    if datamodel_address == 0 then
        engine.log("DataModel not found! Find DataModel first.", 255, 0, 0, 255)
        return
    end

    engine.log("Reading Players service using direct offset path from DataModel...", 255, 255, 255, 255)
    engine.log("Path: DataModel + 0x80 + 0x0 + 0xF0", 255, 255, 255, 255)

    -- Step 1: DataModel + 0x80
    local step1 = proc.read_int64(datamodel_address + PLAYERS_SERVICE_OFFSET)
    if not step1 or step1 == 0 then
        engine.log("Failed at step 1: DataModel + 0x80", 255, 0, 0, 255)
        return
    end
    engine.log("Step 1 result: " .. string.format("0x%X", step1), 255, 255, 255, 255)

    -- Step 2: + 0x0 (should be same address)
    local step2 = proc.read_int64(step1 + PLAYERS_SERVICE_OFFSET2)
    if not step2 or step2 == 0 then
        engine.log("Failed at step 2: +0x0", 255, 0, 0, 255)
        return
    end
    engine.log("Step 2 result: " .. string.format("0x%X", step2), 255, 255, 255, 255)

    -- Step 3: + 0xF0
    players_service_address = proc.read_int64(step2 + PLAYERS_SERVICE_OFFSET3)

    if players_service_address and players_service_address ~= 0 then
        engine.log("Players service found at: " .. string.format("0x%X", players_service_address), 0, 255, 0, 255)
        local players_name = get_object_name(players_service_address)
        local players_class = get_object_class(players_service_address)
        engine.log("Players Service Name: " .. players_name .. ", Class: " .. players_class, 255, 255, 255, 255)
    else
        engine.log("Failed to read Players service from final offset!", 255, 0, 0, 255)
    end
end

function find_local_player()
    if players_service_address == 0 then
        engine.log("Players service not found! Find Players service first.", 255, 0, 0, 255)
        return
    end

    local_player_address = proc.read_int64(players_service_address + LOCAL_PLAYER_OFFSET)
    if local_player_address and local_player_address ~= 0 then
        engine.log("LocalPlayer found at: " .. string.format("0x%X", local_player_address), 0, 255, 0, 255)
        local player_name = get_object_name(local_player_address)
        engine.log("LocalPlayer Name: " .. player_name, 255, 255, 255, 255)
    else
        engine.log("Failed to find LocalPlayer!", 255, 0, 0, 255)
    end
end

-- ========================================
-- GUI INITIALIZATION
-- ========================================

function init_gui()
    engine.log("Initializing GUI...", 0, 255, 0, 255)

    local tab = gui.get_tab("visuals")
    ui_state.main_panel = tab:create_panel("Roblox DataModel Navigation", false)

    -- Process control
    ui_state.attach_button = ui_state.main_panel:add_button("Attach to Roblox", function()
        attach_to_roblox()
    end)

    ui_state.status_text = ui_state.main_panel:add_text("Status: Not attached")

    -- Navigation panel
    ui_state.nav_panel = tab:create_panel("DataModel Navigation", true)
    ui_state.find_datamodel_btn = ui_state.nav_panel:add_button("Find DataModel", function()
        find_datamodel()
    end)
    ui_state.find_workspace_btn = ui_state.nav_panel:add_button("Find Workspace", function()
        find_workspace()
    end)
    ui_state.find_players_btn = ui_state.nav_panel:add_button("Find Players Service", function()
        find_players_service()
    end)
    ui_state.find_localplayer_btn = ui_state.nav_panel:add_button("Find LocalPlayer", function()
        find_local_player()
    end)

    -- Player enumeration panel
    ui_state.player_panel = tab:create_panel("Player Enumeration", true)
    ui_state.get_player_names = ui_state.player_panel:add_button("Get All Player Names", function()
        local names = get_all_player_names()
        if #names > 0 then
            ui_state.player_output:set("Players: " .. table.concat(names, ", "))
        else
            ui_state.player_output:set("No players found")
        end
    end)
    ui_state.get_players_info = ui_state.player_panel:add_button("Get All Player Info", function()
        local players_info = get_all_players_info()
        if #players_info > 0 then
            local info_lines = {}
            for _, player in ipairs(players_info) do
                table.insert(info_lines, player:to_string())
            end
            ui_state.player_output:set(table.concat(info_lines, "\n"))
        else
            ui_state.player_output:set("No players found")
        end
    end)
    ui_state.debug_workspace = ui_state.player_panel:add_button("Debug Workspace Children", function()
        debug_workspace_children()
    end)
    ui_state.simple_player_info = ui_state.player_panel:add_button("Simple Player Info", function()
        get_simple_player_info()
    end)
    ui_state.find_character_offset = ui_state.player_panel:add_button("Find Character Offset", function()
        find_correct_character_offset()
    end)
    ui_state.player_output = ui_state.player_panel:add_text("Player data will appear here")

    -- Features panel
    ui_state.features_panel = tab:create_panel("Features", true)
    ui_state.enable_esp = ui_state.features_panel:add_checkbox("Enable ESP")
    ui_state.enable_info = ui_state.features_panel:add_checkbox("Show Info Display")
    ui_state.esp_color = ui_state.features_panel:add_color_picker("ESP Color", 255, 0, 0, 255)
    ui_state.info_keybind = ui_state.features_panel:add_keybind("Info Toggle", 45, key_mode.toggle) -- INSERT key

    -- Set initial values
    ui_state.enable_esp:set(true)
    ui_state.enable_info:set(true)

    engine.log("GUI initialized successfully!", 0, 255, 0, 255)
end

-- ========================================
-- PROCESS MANAGEMENT
-- ========================================

function attach_to_roblox()
    engine.log("Attempting to attach to Roblox...", 255, 255, 0, 255)

    if proc.attach_by_name("RobloxPlayerBeta.exe") then
        process_attached = true
        base_address = proc.base_address()

        if base_address then
            ui_state.status_text:set("Status: Attached to Roblox (Base: " .. string.format("0x%X", base_address) .. ")")
            engine.log("Successfully attached to Roblox! Base: " .. string.format("0x%X", base_address), 0, 255, 0, 255)

            -- Automatically find all components after successful attachment
            engine.log("Finding DataModel, Workspace, Players service, and LocalPlayer...", 0, 255, 255, 255)
            find_datamodel()
            find_workspace()
            find_players_service()
            find_local_player()
        else
            ui_state.status_text:set("Status: Attached but couldn't get base address")
            engine.log("Attached but failed to get base address!", 255, 0, 0, 255)
        end
    else
        process_attached = false
        ui_state.status_text:set("Status: Failed to attach - Make sure Roblox is running")
        engine.log("Failed to attach to Roblox! Make sure RobloxPlayerBeta.exe is running.", 255, 0, 0, 255)
    end
end



-- ========================================
-- PLAYER ENUMERATION FUNCTIONS
-- ========================================

-- Get all player names using systematic child array approach (based on test all player.lua)
function get_all_player_names()
    if players_service_address == 0 then
        engine.log("Error: Players service address is null.", 255, 0, 0, 255)
        return {}
    end

    local player_names = {}
    engine.log("Getting all player names using child array method...", 255, 255, 0, 255)

    -- 1. Get the address of the children management object from the Players service
    local child_list_object_address = proc.read_int64(players_service_address + OBJECT_CHILDREN)
    if not child_list_object_address or child_list_object_address == 0 then
        engine.log("Error: Could not read child list object address from Players service.", 255, 0, 0, 255)
        return player_names
    end

    engine.log("Child list object found at: " .. string.format("0x%X", child_list_object_address), 255, 255, 255, 255)

    -- 2. Get the pointers to the beginning and end of the child entries array
    local current_child_entry_address = proc.read_int64(child_list_object_address)
    local end_of_child_entries_address = proc.read_int64(child_list_object_address + OBJECT_CHILDREN_END)

    if not current_child_entry_address or not end_of_child_entries_address or
       current_child_entry_address == 0 or end_of_child_entries_address == 0 then
        engine.log("Error: Child array begin or end pointer is null. (Or no players)", 255, 0, 0, 255)
        return player_names
    end

    if current_child_entry_address == end_of_child_entries_address then
        engine.log("No players found (child array is empty).", 255, 255, 0, 255)
        return player_names
    end

    engine.log("Child array: " .. string.format("0x%X", current_child_entry_address) .. " to " .. string.format("0x%X", end_of_child_entries_address), 255, 255, 255, 255)

    -- 3. Iterate through the array of child entries
    local player_count = 0
    while current_child_entry_address < end_of_child_entries_address do
        -- Each entry is 8 bytes (pointer size)
        local player_instance_address = proc.read_int64(current_child_entry_address)

        if player_instance_address and player_instance_address ~= 0 then
            -- Get the player's name using our existing function
            local player_name = get_object_name(player_instance_address)
            local player_class = get_object_class(player_instance_address)

            if player_name and player_class == "Player" and
               player_name ~= "Failed to read name" and player_name ~= "No Name Ptr" then
                table.insert(player_names, player_name)
                player_count = player_count + 1
                engine.log("Found player: " .. player_name .. " at " .. string.format("0x%X", player_instance_address), 0, 255, 0, 255)
            end
        end

        -- Move to the next entry (8 bytes for 64-bit pointers)
        current_child_entry_address = current_child_entry_address + 8
    end

    engine.log("Player enumeration complete! Found " .. player_count .. " players", 0, 255, 0, 255)
    return player_names
end

-- ========================================
-- ADVANCED PLAYER INFORMATION SYSTEM
-- ========================================

-- Structure to hold comprehensive player information
local PlayerInfo = {}
PlayerInfo.__index = PlayerInfo

function PlayerInfo:new(player_address, player_name)
    local obj = {
        address = player_address,
        name = player_name,
        character_address = 0,
        character_name = "",
        humanoid_root_part_address = 0,
        position = { x = 0, y = 0, z = 0 },
        valid = false,
        last_updated = 0,
        -- Extensible fields for future features
        health = 0,
        max_health = 0,
        team = "",
        userid = 0,
        character_parts = {}, -- For storing other parts like Head, Torso, etc.
        properties = {} -- For storing any additional properties
    }
    setmetatable(obj, PlayerInfo)
    return obj
end

function PlayerInfo:update_basic_info()
    if self.address == 0 then return false end

    -- Get UserId
    local userid_ok, userid = pcall(proc.read_int64, self.address + USER_ID_OFFSET)
    if userid_ok and userid and userid > 0 and userid < ********** then
        self.userid = userid
    end

    -- Get Team
    local team_ptr_ok, team_ptr = pcall(proc.read_int64, self.address + TEAM_OFFSET)
    if team_ptr_ok and team_ptr and team_ptr ~= 0 then
        local team_name = get_object_name(team_ptr)
        if team_name and team_name ~= "Failed to read name" then
            self.team = team_name
        end
    end

    return true
end

function PlayerInfo:find_character_in_workspace()
    if workspace_address == 0 or self.name == "" then return false end

    engine.log("Looking for character for player: " .. self.name, 255, 255, 0, 255)

    -- Method 1: Try direct name match
    local character_address = find_child_by_name(workspace_address, self.name)
    if character_address ~= 0 then
        self.character_address = character_address
        self.character_name = get_object_name(character_address) or self.name
        engine.log("Found character via direct name match", 0, 255, 0, 255)
        return true
    end

    -- Method 2: Try to find character using Player.Character property
    local char_ptr_ok, char_ptr = pcall(proc.read_int64, self.address + CHARACTER_OFFSET)
    if char_ptr_ok and char_ptr and char_ptr ~= 0 then
        -- Verify this is actually in workspace by checking if it's a valid character
        local char_name = get_object_name(char_ptr)
        local char_class = get_object_class(char_ptr)

        if char_class == "Model" then
            -- Check if this character has a HumanoidRootPart (good indicator it's a character)
            local hrp_test = find_child_by_name(char_ptr, "HumanoidRootPart")
            if hrp_test ~= 0 then
                self.character_address = char_ptr
                self.character_name = char_name or self.name
                engine.log("Found character via Player.Character property: " .. tostring(char_name), 0, 255, 0, 255)
                return true
            end
        end
    end

    -- Method 3: Search for any Model in workspace that has a Humanoid and HumanoidRootPart
    engine.log("Trying to find character by searching for humanoid models...", 255, 255, 255, 255)
    local character_address = find_character_model_in_workspace()
    if character_address ~= 0 then
        self.character_address = character_address
        self.character_name = get_object_name(character_address) or ("Character_" .. self.name)
        engine.log("Found character via humanoid model search", 0, 255, 0, 255)
        return true
    end

    engine.log("No character found for player: " .. self.name, 255, 255, 0, 255)
    return false
end

function PlayerInfo:find_humanoid_root_part()
    if self.character_address == 0 then return false end

    -- Find HumanoidRootPart in character children
    local hrp_address = find_child_by_name(self.character_address, "HumanoidRootPart")
    if hrp_address ~= 0 then
        self.humanoid_root_part_address = hrp_address
        return true
    end

    return false
end

function PlayerInfo:update_position()
    if self.humanoid_root_part_address == 0 then return false end

    -- Read position directly from HumanoidRootPart
    local pos_x_ok, pos_x = pcall(proc.read_float, self.humanoid_root_part_address + POSITION_OFFSET)
    local pos_y_ok, pos_y = pcall(proc.read_float, self.humanoid_root_part_address + POSITION_OFFSET + 4)
    local pos_z_ok, pos_z = pcall(proc.read_float, self.humanoid_root_part_address + POSITION_OFFSET + 8)

    if pos_x_ok and pos_y_ok and pos_z_ok and pos_x and pos_y and pos_z then
        self.position.x = pos_x
        self.position.y = pos_y
        self.position.z = pos_z
        self.last_updated = os.time()
        return true
    end

    return false
end

function PlayerInfo:update_health()
    if self.character_address == 0 then return false end

    -- Find Humanoid in character
    local humanoid_address = find_child_by_name(self.character_address, "Humanoid")
    if humanoid_address == 0 then return false end

    -- Read health values
    local health_ok, health = pcall(proc.read_float, humanoid_address + HEALTH_OFFSET)
    local max_health_ok, max_health = pcall(proc.read_float, humanoid_address + MAX_HEALTH_OFFSET)

    if health_ok and health then self.health = health end
    if max_health_ok and max_health then self.max_health = max_health end

    return health_ok or max_health_ok
end

function PlayerInfo:find_character_parts()
    if self.character_address == 0 then return false end

    -- Common character parts to look for
    local part_names = {"Head", "Torso", "Left Arm", "Right Arm", "Left Leg", "Right Leg"}

    for _, part_name in ipairs(part_names) do
        local part_address = find_child_by_name(self.character_address, part_name)
        if part_address ~= 0 then
            self.character_parts[part_name] = part_address
        end
    end

    return true
end

function PlayerInfo:update_all()
    local success = true

    -- Update basic player info
    if not self:update_basic_info() then success = false end

    -- Find character in workspace
    if not self:find_character_in_workspace() then
        self.valid = false
        return false
    end

    -- Find HumanoidRootPart
    if not self:find_humanoid_root_part() then
        self.valid = false
        return false
    end

    -- Update position
    if not self:update_position() then success = false end

    -- Update health (optional)
    self:update_health()

    -- Find character parts (optional)
    self:find_character_parts()

    self.valid = success
    return success
end

function PlayerInfo:get_distance_to(other_player)
    if not self.valid or not other_player.valid then return -1 end

    local dx = self.position.x - other_player.position.x
    local dy = self.position.y - other_player.position.y
    local dz = self.position.z - other_player.position.z

    return math.sqrt(dx*dx + dy*dy + dz*dz)
end

function PlayerInfo:to_string()
    local info = string.format("%s (ID: %d)", self.name, self.userid)
    if self.valid then
        info = info .. string.format(" | Pos: (%.1f, %.1f, %.1f)", self.position.x, self.position.y, self.position.z)
        if self.health > 0 then
            info = info .. string.format(" | HP: %.1f/%.1f", self.health, self.max_health)
        end
        if self.team ~= "" then
            info = info .. " | Team: " .. self.team
        end
    else
        info = info .. " | [Invalid/No Character]"
    end
    return info
end

-- Helper function to find a child by name with debugging
function find_child_by_name(parent_address, target_name, log_func)
    if parent_address == 0 or not target_name or target_name == "" then
        local msg = "find_child_by_name: Invalid parameters - parent: " .. tostring(parent_address) .. ", target: " .. tostring(target_name)
        engine.log(msg, 255, 0, 0, 255)
        if log_func then log_func(msg) end
        return 0
    end

    local msg = "Searching for '" .. target_name .. "' in parent " .. string.format("0x%X", parent_address)
    engine.log(msg, 255, 255, 255, 255)
    if log_func then log_func(msg) end

    -- Get children list - use the same method as get_all_player_names
    local child_list_object_address = proc.read_int64(parent_address + OBJECT_CHILDREN)
    if not child_list_object_address or child_list_object_address == 0 then
        local msg = "No children list found for parent " .. string.format("0x%X", parent_address)
        engine.log(msg, 255, 255, 0, 255)
        if log_func then log_func(msg) end
        return 0
    end

    local msg = "Children list object at: " .. string.format("0x%X", child_list_object_address)
    engine.log(msg, 255, 255, 255, 255)
    if log_func then log_func(msg) end

    local current_child_entry_address = proc.read_int64(child_list_object_address)
    local end_of_child_entries_address = proc.read_int64(child_list_object_address + OBJECT_CHILDREN_END)

    if not current_child_entry_address or not end_of_child_entries_address or
       current_child_entry_address == 0 or end_of_child_entries_address == 0 then
        local msg = "Invalid children array pointers"
        engine.log(msg, 255, 255, 0, 255)
        if log_func then log_func(msg) end
        return 0
    end

    local msg = "Scanning children from " .. string.format("0x%X", current_child_entry_address) .. " to " .. string.format("0x%X", end_of_child_entries_address)
    engine.log(msg, 255, 255, 255, 255)
    if log_func then log_func(msg) end

    local child_count = 0
    -- Iterate through children
    while current_child_entry_address < end_of_child_entries_address do
        local child_address = proc.read_int64(current_child_entry_address)

        if child_address and child_address ~= 0 then
            local child_name = get_object_name(child_address)
            local child_class = get_object_class(child_address)
            child_count = child_count + 1

            local msg = "  Child " .. child_count .. ": " .. tostring(child_name) .. " (" .. tostring(child_class) .. ") at " .. string.format("0x%X", child_address)
            engine.log(msg, 255, 255, 255, 255)
            if log_func then log_func(msg) end

            if child_name == target_name then
                local msg = "Found target '" .. target_name .. "' at " .. string.format("0x%X", child_address)
                engine.log(msg, 0, 255, 0, 255)
                if log_func then log_func(msg) end
                return child_address
            end
        end

        current_child_entry_address = current_child_entry_address + 8
    end

    local msg = "Target '" .. target_name .. "' not found among " .. child_count .. " children"
    engine.log(msg, 255, 255, 0, 255)
    if log_func then log_func(msg) end
    return 0
end

-- Find character models in workspace by looking for humanoid structure
function find_character_model_in_workspace(log_func)
    if workspace_address == 0 then return 0 end

    local child_list_object_address = proc.read_int64(workspace_address + OBJECT_CHILDREN)
    if not child_list_object_address or child_list_object_address == 0 then return 0 end

    local current_child_entry_address = proc.read_int64(child_list_object_address)
    local end_of_child_entries_address = proc.read_int64(child_list_object_address + OBJECT_CHILDREN_END)

    if not current_child_entry_address or not end_of_child_entries_address or
       current_child_entry_address == 0 or end_of_child_entries_address == 0 then
        return 0
    end

    -- Look for Models that have both Humanoid and HumanoidRootPart
    while current_child_entry_address < end_of_child_entries_address do
        local child_address = proc.read_int64(current_child_entry_address)

        if child_address and child_address ~= 0 then
            local child_class = get_object_class(child_address)

            if child_class == "Model" then
                -- Check if this model has both Humanoid and HumanoidRootPart
                if log_func then log_func("Checking Model for Humanoid and HumanoidRootPart...") end
                local humanoid_addr = find_child_by_name(child_address, "Humanoid", log_func)
                local hrp_addr = find_child_by_name(child_address, "HumanoidRootPart", log_func)

                if humanoid_addr ~= 0 and hrp_addr ~= 0 then
                    local msg = "Found potential character model at " .. string.format("0x%X", child_address)
                    engine.log(msg, 0, 255, 255, 255)
                    if log_func then log_func(msg) end
                    return child_address
                end
            end
        end

        current_child_entry_address = current_child_entry_address + 8
    end

    return 0
end

-- Improved string reading with better error handling
function read_roblox_string_safe(string_address)
    if not string_address or string_address == 0 then
        return ""
    end

    -- Try multiple methods to read the string
    local methods = {
        -- Method 1: Direct string read
        function()
            return proc.read_string(string_address, 100)
        end,
        -- Method 2: Read with different encoding
        function()
            local length_ok, length = pcall(proc.read_int32, string_address)
            if length_ok and length and length > 0 and length < 200 then
                local str_ok, str = pcall(proc.read_string, string_address + 4, length)
                if str_ok then return str end
            end
            return nil
        end,
        -- Method 3: Try reading as wide string
        function()
            local str_ok, str = pcall(proc.read_string, string_address, 50)
            if str_ok and str and str ~= "" then
                -- Filter out non-printable characters
                local clean_str = ""
                for i = 1, #str do
                    local byte = string.byte(str, i)
                    if byte >= 32 and byte <= 126 then
                        clean_str = clean_str .. string.char(byte)
                    end
                end
                if #clean_str > 0 then return clean_str end
            end
            return nil
        end
    }

    for i, method in ipairs(methods) do
        local success, result = pcall(method)
        if success and result and result ~= "" and not result:match("^%?+$") then
            return result
        end
    end

    return "UnreadableName"
end

-- Debug function to see what's in workspace
function debug_workspace_children()
    if workspace_address == 0 then
        engine.log("Workspace not found!", 255, 0, 0, 255)
        return
    end

    engine.log("=== DEBUGGING WORKSPACE CHILDREN ===", 0, 255, 255, 255)
    engine.log("Workspace address: " .. string.format("0x%X", workspace_address), 255, 255, 255, 255)

    local child_list_object_address = proc.read_int64(workspace_address + OBJECT_CHILDREN)
    if not child_list_object_address or child_list_object_address == 0 then
        engine.log("No children list found for workspace", 255, 0, 0, 255)
        return
    end

    engine.log("Children list object at: " .. string.format("0x%X", child_list_object_address), 255, 255, 255, 255)

    local current_child_entry_address = proc.read_int64(child_list_object_address)
    local end_of_child_entries_address = proc.read_int64(child_list_object_address + OBJECT_CHILDREN_END)

    if not current_child_entry_address or not end_of_child_entries_address or
       current_child_entry_address == 0 or end_of_child_entries_address == 0 then
        engine.log("Invalid children array pointers", 255, 0, 0, 255)
        return
    end

    engine.log("Scanning workspace children from " .. string.format("0x%X", current_child_entry_address) .. " to " .. string.format("0x%X", end_of_child_entries_address), 255, 255, 255, 255)

    local child_count = 0
    local models_found = {}

    while current_child_entry_address < end_of_child_entries_address do
        local child_address = proc.read_int64(current_child_entry_address)

        if child_address and child_address ~= 0 then
            local child_name = get_object_name(child_address)
            local child_class = get_object_class(child_address)
            child_count = child_count + 1

            engine.log("  Child " .. child_count .. ": '" .. tostring(child_name) .. "' (" .. tostring(child_class) .. ") at " .. string.format("0x%X", child_address), 255, 255, 255, 255)

            -- Look for potential character models
            if child_class == "Model" then
                table.insert(models_found, {name = child_name, address = child_address})
            end

            -- Limit output to prevent spam
            if child_count >= 50 then
                engine.log("  ... (stopping at 50 children to prevent spam)", 255, 255, 0, 255)
                break
            end
        end

        current_child_entry_address = current_child_entry_address + 8
    end

    engine.log("Total children found: " .. child_count, 0, 255, 0, 255)
    engine.log("Models found: " .. #models_found, 0, 255, 0, 255)

    for i, model in ipairs(models_found) do
        engine.log("  Model " .. i .. ": " .. model.name, 0, 255, 255, 255)
        if i >= 20 then
            engine.log("  ... (showing first 20 models only)", 255, 255, 0, 255)
            break
        end
    end

    engine.log("=== END WORKSPACE DEBUG ===", 0, 255, 255, 255)
end

-- Get comprehensive information for all players
function get_all_players_info()
    if players_service_address == 0 then
        engine.log("Players service not found!", 255, 0, 0, 255)
        return {}
    end

    engine.log("Getting comprehensive player information...", 255, 255, 0, 255)

    local players_info = {}
    local player_names = get_all_player_names()

    if #player_names == 0 then
        engine.log("No players found", 255, 255, 0, 255)
        return players_info
    end

    -- Get player addresses using the same method as get_all_player_names
    local child_list_object_address = proc.read_int64(players_service_address + OBJECT_CHILDREN)
    if not child_list_object_address or child_list_object_address == 0 then return players_info end

    local current_child_entry_address = proc.read_int64(child_list_object_address)
    local end_of_child_entries_address = proc.read_int64(child_list_object_address + OBJECT_CHILDREN_END)

    if not current_child_entry_address or not end_of_child_entries_address then return players_info end

    -- Create PlayerInfo objects for each player
    while current_child_entry_address < end_of_child_entries_address do
        local player_address = proc.read_int64(current_child_entry_address)

        if player_address and player_address ~= 0 then
            local player_name = get_object_name(player_address)
            local player_class = get_object_class(player_address)

            if player_name and player_class == "Player" and
               player_name ~= "Failed to read name" and player_name ~= "No Name Ptr" then

                -- Create PlayerInfo object and update all information
                local player_info = PlayerInfo:new(player_address, player_name)
                player_info:update_all()

                table.insert(players_info, player_info)

                if player_info.valid then
                    engine.log("✓ " .. player_info:to_string(), 0, 255, 0, 255)
                else
                    engine.log("✗ " .. player_info.name .. " (No character found)", 255, 255, 0, 255)
                end
            end
        end

        current_child_entry_address = current_child_entry_address + 8
    end

    engine.log("Player info collection complete! Found " .. #players_info .. " players", 0, 255, 0, 255)
    return players_info
end

-- Simple approach using Player.Character property directly
function get_simple_player_info()
    if players_service_address == 0 then
        engine.log("Players service not found!", 255, 0, 0, 255)
        return
    end

    engine.log("=== SIMPLE PLAYER INFO (Using Player.Character) ===", 0, 255, 255, 255)

    local player_names = get_all_player_names()
    if #player_names == 0 then
        engine.log("No players found", 255, 255, 0, 255)
        return
    end

    -- Get player addresses using the same method as get_all_player_names
    local child_list_object_address = proc.read_int64(players_service_address + OBJECT_CHILDREN)
    if not child_list_object_address or child_list_object_address == 0 then return end

    local current_child_entry_address = proc.read_int64(child_list_object_address)
    local end_of_child_entries_address = proc.read_int64(child_list_object_address + OBJECT_CHILDREN_END)

    if not current_child_entry_address or not end_of_child_entries_address then return end

    local player_count = 0

    -- Process each player
    while current_child_entry_address < end_of_child_entries_address do
        local player_address = proc.read_int64(current_child_entry_address)

        if player_address and player_address ~= 0 then
            local player_name = get_object_name(player_address)
            local player_class = get_object_class(player_address)

            if player_name and player_class == "Player" and
               player_name ~= "Failed to read name" and player_name ~= "No Name Ptr" then

                player_count = player_count + 1
                engine.log("Player " .. player_count .. ": " .. player_name, 255, 255, 255, 255)

                -- Get UserId
                local userid_ok, userid = pcall(proc.read_int64, player_address + USER_ID_OFFSET)
                if userid_ok and userid then
                    engine.log("  UserId: " .. userid, 255, 255, 255, 255)
                end

                -- Try to get character directly from Player.Character
                local char_ok, char_address = pcall(proc.read_int64, player_address + CHARACTER_OFFSET)
                if char_ok and char_address and char_address ~= 0 then
                    engine.log("  Character address: " .. string.format("0x%X", char_address), 255, 255, 255, 255)

                    local char_name = get_object_name(char_address)
                    local char_class = get_object_class(char_address)
                    engine.log("  Character name: " .. tostring(char_name) .. " (" .. tostring(char_class) .. ")", 255, 255, 255, 255)

                    -- Try to find HumanoidRootPart directly using offset
                    local hrp_ok, hrp_address = pcall(proc.read_int64, char_address + ROOT_PART_OFFSET)
                    if hrp_ok and hrp_address and hrp_address ~= 0 then
                        engine.log("  HumanoidRootPart address: " .. string.format("0x%X", hrp_address), 255, 255, 255, 255)

                        -- Try to read position
                        local pos_x_ok, pos_x = pcall(proc.read_float, hrp_address + POSITION_OFFSET)
                        local pos_y_ok, pos_y = pcall(proc.read_float, hrp_address + POSITION_OFFSET + 4)
                        local pos_z_ok, pos_z = pcall(proc.read_float, hrp_address + POSITION_OFFSET + 8)

                        if pos_x_ok and pos_y_ok and pos_z_ok and pos_x and pos_y and pos_z then
                            engine.log("  Position: (" .. string.format("%.1f", pos_x) .. ", " .. string.format("%.1f", pos_y) .. ", " .. string.format("%.1f", pos_z) .. ")", 0, 255, 0, 255)
                        else
                            engine.log("  Position: Failed to read from HRP", 255, 0, 0, 255)
                        end
                    else
                        engine.log("  HumanoidRootPart: Not found via direct offset", 255, 255, 0, 255)
                    end
                else
                    engine.log("  Character: Not found or invalid", 255, 0, 0, 255)
                end

                engine.log("", 255, 255, 255, 255) -- Empty line for readability
            end
        end

        current_child_entry_address = current_child_entry_address + 8
    end

    engine.log("=== END SIMPLE PLAYER INFO ===", 0, 255, 255, 255)
end

-- Function to find the correct CHARACTER_OFFSET by scanning player memory
function find_correct_character_offset()
    if players_service_address == 0 then
        engine.log("Players service not found!", 255, 0, 0, 255)
        return
    end

    engine.log("=== FINDING CORRECT CHARACTER OFFSET ===", 0, 255, 255, 255)

    -- Initialize log file
    local log_filename = "character_offset_scan_" .. tostring(time.unix()) .. ".txt"
    local log_lines = {}

    local function add_log(message)
        engine.log(message, 255, 255, 255, 255)
        table.insert(log_lines, "[SCAN] " .. message)
    end

    local function add_log_colored(message, r, g, b, a)
        engine.log(message, r, g, b, a)
        table.insert(log_lines, "[SCAN] " .. message)
    end

    add_log("=== CHARACTER OFFSET SCANNING STARTED ===")
    add_log("Engine time: " .. tostring(time.unix()))
    add_log("")

    -- Get the first player to scan
    local child_list_object_address = proc.read_int64(players_service_address + OBJECT_CHILDREN)
    if not child_list_object_address or child_list_object_address == 0 then
        add_log_colored("ERROR: No players found", 255, 0, 0, 255)
        save_log_file(log_filename, log_lines)
        return
    end

    local current_child_entry_address = proc.read_int64(child_list_object_address)
    local end_of_child_entries_address = proc.read_int64(child_list_object_address + OBJECT_CHILDREN_END)

    if not current_child_entry_address or not end_of_child_entries_address then
        add_log_colored("ERROR: Invalid child array", 255, 0, 0, 255)
        save_log_file(log_filename, log_lines)
        return
    end

    local player_address = proc.read_int64(current_child_entry_address)
    if not player_address or player_address == 0 then
        add_log_colored("ERROR: No valid player found", 255, 0, 0, 255)
        save_log_file(log_filename, log_lines)
        return
    end

    local player_name = get_object_name(player_address)
    add_log("Scanning player: " .. tostring(player_name) .. " at " .. string.format("0x%X", player_address))
    add_log("Players service address: " .. string.format("0x%X", players_service_address))
    add_log("Current CHARACTER_OFFSET: " .. string.format("0x%X", CHARACTER_OFFSET))
    add_log("")

    -- Test current offset first
    add_log("Testing current CHARACTER_OFFSET (" .. string.format("0x%X", CHARACTER_OFFSET) .. "):")
    local current_char_ok, current_char_addr = pcall(proc.read_int64, player_address + CHARACTER_OFFSET)
    if current_char_ok and current_char_addr then
        add_log("  Current offset points to: " .. string.format("0x%X", current_char_addr))
        if current_char_addr > 0x100000 and current_char_addr < 0x7FFFFFFFFFFF then
            local char_name = get_object_name(current_char_addr)
            local char_class = get_object_class(current_char_addr)
            add_log("  Object name: " .. tostring(char_name) .. " (" .. tostring(char_class) .. ")")
        else
            add_log("  Invalid address range - too small or too large")
        end
    else
        add_log("  Failed to read from current offset")
    end
    add_log("")

    add_log("Scanning for character pointer in player memory (0x100 to 0x400)...")

    local found_offsets = {}

    for offset = 0x100, 0x400, 8 do -- Scan from 0x100 to 0x400 in 8-byte steps
        local success, potential_char_addr = pcall(proc.read_int64, player_address + offset)

        if success and potential_char_addr and potential_char_addr > 0x100000 and potential_char_addr < 0x7FFFFFFFFFFF then
            -- Check if this looks like a character (Model class)
            local char_name_ok, char_name = pcall(get_object_name, potential_char_addr)
            local char_class_ok, char_class = pcall(get_object_class, potential_char_addr)

            if char_class_ok and char_class == "Model" and char_name_ok and char_name then
                add_log("Found Model at offset " .. string.format("0x%X", offset) .. ": " .. char_name)

                -- Additional validation: check if this model has humanoid-like children
                local has_humanoid = false
                local has_hrp = false

                -- Try to find Humanoid and HumanoidRootPart in this model
                add_log("  Checking for Humanoid and HumanoidRootPart...")
                add_log("  --- Searching for Humanoid ---")
                local humanoid_addr = find_child_by_name(potential_char_addr, "Humanoid", add_log)
                add_log("  --- Searching for HumanoidRootPart ---")
                local hrp_addr = find_child_by_name(potential_char_addr, "HumanoidRootPart", add_log)

                if humanoid_addr ~= 0 then
                    has_humanoid = true
                    add_log("  ✓ Humanoid found at: " .. string.format("0x%X", humanoid_addr))
                else
                    add_log("  ✗ Humanoid not found")
                end

                if hrp_addr ~= 0 then
                    has_hrp = true
                    add_log("  ✓ HumanoidRootPart found at: " .. string.format("0x%X", hrp_addr))
                else
                    add_log("  ✗ HumanoidRootPart not found")
                end

                local confidence = 0
                if has_humanoid then confidence = confidence + 1 end
                if has_hrp then confidence = confidence + 1 end
                if char_name == player_name then
                    confidence = confidence + 2
                    add_log("  ✓ Character name matches player name!")
                else
                    add_log("  ✗ Character name (" .. char_name .. ") doesn't match player name (" .. player_name .. ")")
                end

                table.insert(found_offsets, {
                    offset = offset,
                    address = potential_char_addr,
                    name = char_name,
                    class = char_class,
                    has_humanoid = has_humanoid,
                    has_hrp = has_hrp,
                    confidence = confidence
                })

                local confidence_text = ""
                if confidence >= 3 then confidence_text = " [HIGH CONFIDENCE]"
                elseif confidence >= 2 then confidence_text = " [MEDIUM CONFIDENCE]"
                else confidence_text = " [LOW CONFIDENCE]" end

                add_log("  Final confidence: " .. confidence .. "/4" .. confidence_text)
                add_log("")

                engine.log("  Offset " .. string.format("0x%X", offset) .. ": " .. char_name .. " (" .. char_class .. ")" .. confidence_text, 0, 255, 255, 255)
                engine.log("    Address: " .. string.format("0x%X", potential_char_addr) .. " | Humanoid: " .. tostring(has_humanoid) .. " | HRP: " .. tostring(has_hrp), 255, 255, 255, 255)
            end
        end
    end

    if #found_offsets == 0 then
        add_log_colored("ERROR: No potential character addresses found!", 255, 0, 0, 255)
        add_log("This could mean:")
        add_log("  - Character is not spawned yet")
        add_log("  - Player is in lobby/menu")
        add_log("  - Memory structure is completely different")
        save_log_file(log_filename, log_lines)
        return
    end

    -- Sort by confidence
    table.sort(found_offsets, function(a, b) return a.confidence > b.confidence end)

    add_log("=== SCAN RESULTS (sorted by confidence) ===")
    for i, result in ipairs(found_offsets) do
        add_log(i .. ". Offset " .. string.format("0x%X", result.offset) .. " -> " .. result.name .. " (confidence: " .. result.confidence .. "/4)")
        add_log("   Address: " .. string.format("0x%X", result.address))
        add_log("   Has Humanoid: " .. tostring(result.has_humanoid))
        add_log("   Has HumanoidRootPart: " .. tostring(result.has_hrp))
        add_log("")
    end

    engine.log("=== RESULTS (sorted by confidence) ===", 0, 255, 255, 255)
    for i, result in ipairs(found_offsets) do
        engine.log(i .. ". Offset " .. string.format("0x%X", result.offset) .. " -> " .. result.name .. " (confidence: " .. result.confidence .. ")", 255, 255, 255, 255)
    end

    if found_offsets[1].confidence >= 2 then
        local best_offset = found_offsets[1].offset
        add_log("=== RECOMMENDATION ===")
        add_log("RECOMMENDED: Use CHARACTER_OFFSET = " .. string.format("0x%X", best_offset))
        add_log("This offset points to: " .. found_offsets[1].name .. " with confidence " .. found_offsets[1].confidence .. "/4")
        add_log("")

        engine.log("RECOMMENDED: Use CHARACTER_OFFSET = " .. string.format("0x%X", best_offset), 0, 255, 0, 255)
        engine.log("This offset points to: " .. found_offsets[1].name .. " with confidence " .. found_offsets[1].confidence, 0, 255, 0, 255)

        -- Test the position reading with this offset
        add_log("=== POSITION TESTING ===")
        engine.log("Testing position reading with recommended offset...", 255, 255, 0, 255)
        test_position_reading_with_offset_logged(player_address, best_offset, log_lines, add_log)
    else
        add_log("=== NO HIGH CONFIDENCE RESULT ===")
        add_log("No high-confidence character offset found. Manual investigation needed.")
        add_log("Best result has confidence: " .. found_offsets[1].confidence .. "/4")
        engine.log("No high-confidence character offset found. Manual investigation needed.", 255, 255, 0, 255)
    end

    add_log("")
    add_log("=== SCAN COMPLETE ===")
    add_log("Log saved to: " .. log_filename)

    -- Save the log file
    save_log_file(log_filename, log_lines)

    engine.log("=== END CHARACTER OFFSET SEARCH ===", 0, 255, 255, 255)
    engine.log("Detailed log saved to: " .. log_filename, 0, 255, 255, 255)
end

-- Test position reading with a specific character offset
function test_position_reading_with_offset(player_address, char_offset)
    local char_ok, char_address = pcall(proc.read_int64, player_address + char_offset)
    if not char_ok or not char_address or char_address == 0 then
        engine.log("Failed to read character address", 255, 0, 0, 255)
        return
    end

    engine.log("Testing with character at: " .. string.format("0x%X", char_address), 255, 255, 255, 255)

    -- Try to find HumanoidRootPart
    local hrp_addr = find_child_by_name(char_address, "HumanoidRootPart")
    if hrp_addr == 0 then
        engine.log("HumanoidRootPart not found in character", 255, 255, 0, 255)
        return
    end

    engine.log("HumanoidRootPart found at: " .. string.format("0x%X", hrp_addr), 0, 255, 255, 255)

    -- Try to read position
    local pos_x_ok, pos_x = pcall(proc.read_float, hrp_addr + POSITION_OFFSET)
    local pos_y_ok, pos_y = pcall(proc.read_float, hrp_addr + POSITION_OFFSET + 4)
    local pos_z_ok, pos_z = pcall(proc.read_float, hrp_addr + POSITION_OFFSET + 8)

    if pos_x_ok and pos_y_ok and pos_z_ok and pos_x and pos_y and pos_z then
        engine.log("SUCCESS! Position: (" .. string.format("%.1f", pos_x) .. ", " .. string.format("%.1f", pos_y) .. ", " .. string.format("%.1f", pos_z) .. ")", 0, 255, 0, 255)
    else
        engine.log("Failed to read position from HumanoidRootPart", 255, 0, 0, 255)

        -- Try scanning HumanoidRootPart for position
        engine.log("Scanning HumanoidRootPart for position offset...", 255, 255, 255, 255)
        for pos_offset = 0x100, 0x200, 4 do
            local test_x_ok, test_x = pcall(proc.read_float, hrp_addr + pos_offset)
            local test_y_ok, test_y = pcall(proc.read_float, hrp_addr + pos_offset + 4)
            local test_z_ok, test_z = pcall(proc.read_float, hrp_addr + pos_offset + 8)

            if test_x_ok and test_y_ok and test_z_ok and test_x and test_y and test_z then
                -- Check if these look like reasonable position values
                if math.abs(test_x) < 10000 and math.abs(test_y) < 10000 and math.abs(test_z) < 10000 then
                    engine.log("Potential position at offset " .. string.format("0x%X", pos_offset) .. ": (" .. string.format("%.1f", test_x) .. ", " .. string.format("%.1f", test_y) .. ", " .. string.format("%.1f", test_z) .. ")", 0, 255, 255, 255)
                end
            end
        end
    end
end

-- Test position reading with a specific character offset (with logging)
function test_position_reading_with_offset_logged(player_address, char_offset, log_lines, add_log)
    local char_ok, char_address = pcall(proc.read_int64, player_address + char_offset)
    if not char_ok or not char_address or char_address == 0 then
        add_log("ERROR: Failed to read character address")
        return
    end

    add_log("Testing with character at: " .. string.format("0x%X", char_address))

    -- Try to find HumanoidRootPart
    add_log("Looking for HumanoidRootPart in character...")
    add_log("--- Detailed HumanoidRootPart Search ---")
    local hrp_addr = find_child_by_name(char_address, "HumanoidRootPart", add_log)
    add_log("--- End HumanoidRootPart Search ---")
    if hrp_addr == 0 then
        add_log("ERROR: HumanoidRootPart not found in character")
        add_log("This character may not be a valid player character")
        return
    end

    add_log("✓ HumanoidRootPart found at: " .. string.format("0x%X", hrp_addr))
    add_log("Testing position reading with current POSITION_OFFSET (" .. string.format("0x%X", POSITION_OFFSET) .. ")...")

    -- Try to read position
    local pos_x_ok, pos_x = pcall(proc.read_float, hrp_addr + POSITION_OFFSET)
    local pos_y_ok, pos_y = pcall(proc.read_float, hrp_addr + POSITION_OFFSET + 4)
    local pos_z_ok, pos_z = pcall(proc.read_float, hrp_addr + POSITION_OFFSET + 8)

    if pos_x_ok and pos_y_ok and pos_z_ok and pos_x and pos_y and pos_z then
        add_log("SUCCESS! Position: (" .. string.format("%.1f", pos_x) .. ", " .. string.format("%.1f", pos_y) .. ", " .. string.format("%.1f", pos_z) .. ")")
        add_log("✓ Current POSITION_OFFSET (" .. string.format("0x%X", POSITION_OFFSET) .. ") is working correctly!")
        engine.log("SUCCESS! Position: (" .. string.format("%.1f", pos_x) .. ", " .. string.format("%.1f", pos_y) .. ", " .. string.format("%.1f", pos_z) .. ")", 0, 255, 0, 255)
    else
        add_log("ERROR: Failed to read position from HumanoidRootPart using current POSITION_OFFSET")
        add_log("Current POSITION_OFFSET (" .. string.format("0x%X", POSITION_OFFSET) .. ") may be incorrect")
        add_log("")
        add_log("Scanning HumanoidRootPart for correct position offset...")

        local found_position = false
        for pos_offset = 0x100, 0x200, 4 do
            local test_x_ok, test_x = pcall(proc.read_float, hrp_addr + pos_offset)
            local test_y_ok, test_y = pcall(proc.read_float, hrp_addr + pos_offset + 4)
            local test_z_ok, test_z = pcall(proc.read_float, hrp_addr + pos_offset + 8)

            if test_x_ok and test_y_ok and test_z_ok and test_x and test_y and test_z then
                -- Check if these look like reasonable position values
                if math.abs(test_x) < 10000 and math.abs(test_y) < 10000 and math.abs(test_z) < 10000 then
                    add_log("Potential position at offset " .. string.format("0x%X", pos_offset) .. ": (" .. string.format("%.1f", test_x) .. ", " .. string.format("%.1f", test_y) .. ", " .. string.format("%.1f", test_z) .. ")")
                    found_position = true
                    engine.log("Potential position at offset " .. string.format("0x%X", pos_offset) .. ": (" .. string.format("%.1f", test_x) .. ", " .. string.format("%.1f", test_y) .. ", " .. string.format("%.1f", test_z) .. ")", 0, 255, 255, 255)
                end
            end
        end

        if not found_position then
            add_log("No valid position data found in HumanoidRootPart")
            add_log("The HumanoidRootPart structure may be different than expected")
        end
    end
end

-- Save log lines to a text file using File System API
function save_log_file(filename, log_lines)
    local success, error_msg = pcall(function()
        -- Add header with scan information
        local header_lines = {
            "=== ROBLOX CHARACTER OFFSET SCAN LOG ===",
            "Generated by: Roblox DataModel Navigation Script",
            "Engine time: " .. tostring(time.unix()),
            "File: " .. filename,
            "",
            "=== SCAN DATA ===",
            ""
        }

        -- Combine header with log lines
        local all_lines = {}
        for _, line in ipairs(header_lines) do
            table.insert(all_lines, line)
        end
        for _, line in ipairs(log_lines) do
            table.insert(all_lines, line)
        end

        -- Add footer
        table.insert(all_lines, "")
        table.insert(all_lines, "=== END OF LOG ===")

        -- Create final content
        local content = table.concat(all_lines, "\n")

        -- Use File System API to write the file
        fs.write_to_file(filename, content)
    end)

    if success then
        engine.log("✓ Log file saved successfully: " .. filename, 0, 255, 0, 255)
        engine.log("✓ File location: Documents/My Games/" .. filename, 0, 255, 255, 255)
    else
        engine.log("✗ Failed to save log file: " .. tostring(error_msg), 255, 0, 0, 255)
    end
end

-- ========================================
-- RENDERING FUNCTIONS
-- ========================================

function init_rendering()
    font_handle = render.create_font("arial.ttf", 16)
    if not font_handle then
        font_handle = render.create_font("C:/Windows/Fonts/arial.ttf", 16)
    end

    if font_handle then
        engine.log("Font created successfully!", 0, 255, 0, 255)
    else
        engine.log("Failed to create font, text rendering may not work", 255, 255, 0, 255)
    end
end

function render_overlay()
    if not ui_state.enable_esp or not ui_state.enable_esp:get() then
        return
    end

    local screen_width, screen_height = render.get_viewport_size()
    local box_size = 100
    local center_x = screen_width / 2 - box_size / 2
    local center_y = screen_height / 2 - box_size / 2

    local r, g, b, a = ui_state.esp_color:get()

    -- Draw ESP rectangle
    render.draw_rectangle(center_x, center_y, box_size, box_size, r, g, b, a, 2, false, 0)

    -- Draw corner indicators
    local corner_size = 10
    render.draw_line(center_x, center_y, center_x + corner_size, center_y, r, g, b, a, 2)
    render.draw_line(center_x, center_y, center_x, center_y + corner_size, r, g, b, a, 2)

    render.draw_line(center_x + box_size, center_y, center_x + box_size - corner_size, center_y, r, g, b, a, 2)
    render.draw_line(center_x + box_size, center_y, center_x + box_size, center_y + corner_size, r, g, b, a, 2)

    render.draw_line(center_x, center_y + box_size, center_x + corner_size, center_y + box_size, r, g, b, a, 2)
    render.draw_line(center_x, center_y + box_size, center_x, center_y + box_size - corner_size, r, g, b, a, 2)

    render.draw_line(center_x + box_size, center_y + box_size, center_x + box_size - corner_size, center_y + box_size, r,
        g, b, a, 2)
    render.draw_line(center_x + box_size, center_y + box_size, center_x + box_size, center_y + box_size - corner_size, r,
        g, b, a, 2)

    -- Draw center dot
    render.draw_circle(screen_width / 2, screen_height / 2, 3, 255, 255, 255, 255, 1, true)
end

function render_info_display()
    if not ui_state.enable_info or not ui_state.enable_info:get() then
        return
    end

    if not ui_state.info_keybind:is_active() then
        return
    end

    if not font_handle then
        return
    end

    local info_x = 10
    local info_y = 400
    local line_height = 20
    local current_y = info_y

    -- Background
    render.draw_rectangle(info_x - 5, info_y - 5, 400, 200, 0, 0, 0, 150, 1, true, 5)

    -- Title
    render.draw_text(font_handle, "Roblox DataModel Navigation Info", info_x, current_y, 255, 255, 255, 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- Process status
    local status_text = process_attached and "Process: Attached" or "Process: Not Attached"
    local status_color = process_attached and { 0, 255, 0 } or { 255, 0, 0 }
    render.draw_text(font_handle, status_text, info_x, current_y, status_color[1], status_color[2], status_color[3], 255,
        1, 0, 0, 0, 255)
    current_y = current_y + line_height

    -- DataModel status
    local dm_status = datamodel_address ~= 0 and ("DataModel: " .. string.format("0x%X", datamodel_address)) or
        "DataModel: Not Found"
    local dm_color = datamodel_address ~= 0 and { 0, 255, 0 } or { 255, 255, 0 }
    render.draw_text(font_handle, dm_status, info_x, current_y, dm_color[1], dm_color[2], dm_color[3], 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- Workspace status
    local ws_status = workspace_address ~= 0 and ("Workspace: " .. string.format("0x%X", workspace_address)) or
        "Workspace: Not Found"
    local ws_color = workspace_address ~= 0 and { 0, 255, 0 } or { 255, 255, 0 }
    render.draw_text(font_handle, ws_status, info_x, current_y, ws_color[1], ws_color[2], ws_color[3], 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- Players status
    local ps_status = players_service_address ~= 0 and ("Players: " .. string.format("0x%X", players_service_address)) or
        "Players: Not Found"
    local ps_color = players_service_address ~= 0 and { 0, 255, 0 } or { 255, 255, 0 }
    render.draw_text(font_handle, ps_status, info_x, current_y, ps_color[1], ps_color[2], ps_color[3], 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- LocalPlayer status
    local lp_status = local_player_address ~= 0 and ("LocalPlayer: " .. string.format("0x%X", local_player_address)) or
        "LocalPlayer: Not Found"
    local lp_color = local_player_address ~= 0 and { 0, 255, 0 } or { 255, 255, 0 }
    render.draw_text(font_handle, lp_status, info_x, current_y, lp_color[1], lp_color[2], lp_color[3], 255, 1, 0, 0, 0,
        255)
    current_y = current_y + line_height

    -- FPS
    -- local fps = render.get_fps()
    -- render.draw_text(font_handle, "Overlay FPS: " .. tostring(fps), info_x, current_y, 255, 255, 255, 255, 1, 0, 0, 0,
    --     255)
    -- current_y = current_y + line_height

    -- Controls info
    render.draw_text(font_handle, "Controls: \nINSERT=Toggle Info, \nF5=Get Player Names, \nF6=Get Player Info",
        info_x,
        current_y,
        200, 200, 200, 255, 1, 0, 0, 0, 255)
end

-- Display connected players list
function render_players_display()
    if not ui_state.enable_info or not ui_state.enable_info:get() then
        return
    end

    if not ui_state.info_keybind:is_active() then
        return
    end

    if not font_handle or #connected_players == 0 then
        return
    end

    local screen_width, screen_height = render.get_viewport_size()
    local players_x = screen_width - 420
    local players_y = 10
    local line_height = 18
    local current_y = players_y

    -- Background for players list
    local bg_height = math.min(#connected_players * line_height + 40, screen_height - 20)
    render.draw_rectangle(players_x - 5, players_y - 5, 410, bg_height, 0, 0, 0, 150, 1, true, 5)

    -- Title
    render.draw_text(font_handle, "Connected Players (" .. #connected_players .. ")", players_x, current_y, 255, 255, 0,
        255, 1, 0, 0, 0, 255)
    current_y = current_y + line_height + 5

    -- Player list
    for i, player in ipairs(connected_players) do
        if current_y > screen_height - 40 then
            render.draw_text(font_handle, "... and " .. (#connected_players - i + 1) .. " more", players_x, current_y,
                200, 200, 200, 255, 1, 0, 0, 0, 255)
            break
        end

        local player_text = player.name or "Unknown"
        if player.userid then
            player_text = player_text .. " (ID: " .. player.userid .. ")"
        end

        -- Player name and ID
        render.draw_text(font_handle, player_text, players_x, current_y, 255, 255, 255, 255, 1, 0, 0, 0, 255)
        current_y = current_y + line_height

        -- Position if available
        if player.position then
            local pos_text = string.format("  Pos: (%.1f, %.1f, %.1f)", player.position.x, player.position.y,
                player.position.z)
            render.draw_text(font_handle, pos_text, players_x, current_y, 0, 255, 255, 255, 1, 0, 0, 0, 255)
            current_y = current_y + line_height
        end
    end
end

-- ========================================
-- MAIN LOOP AND EVENT HANDLING
-- ========================================

function on_engine_tick()
    -- Check if process is still alive
    if process_attached and proc.did_exit() then
        process_attached = false
        base_address = 0
        datamodel_address = 0
        workspace_address = 0
        players_service_address = 0
        local_player_address = 0
        ui_state.status_text:set("Status: Process exited")
        engine.log("Roblox process exited!", 255, 255, 0, 255)
    end

    -- Render overlay elements
    render_overlay()
    render_info_display()
    render_players_display()

    -- Hotkey handling

    if input.is_key_pressed(116) then -- F5 key
        local names = get_all_player_names()
        if #names > 0 then
            engine.log("Players found: " .. table.concat(names, ", "), 0, 255, 0, 255)
        else
            engine.log("No players found", 255, 255, 0, 255)
        end
    end

    if input.is_key_pressed(117) then -- F6 key
        local players_info = get_all_players_info()
        if #players_info > 0 then
            engine.log("=== COMPREHENSIVE PLAYER INFO ===", 0, 255, 255, 255)
            for _, player in ipairs(players_info) do
                engine.log(player:to_string(), 255, 255, 255, 255)
            end
        else
            engine.log("No players found", 255, 255, 0, 255)
        end
    end

    if input.is_key_pressed(118) then -- F7 key
        debug_workspace_children()
    end

    if input.is_key_pressed(119) then -- F8 key
        get_simple_player_info()
    end

    if input.is_key_pressed(120) then -- F9 key
        find_correct_character_offset()
    end
end

function on_unload()
    engine.log("Roblox DataModel Navigation Script unloading...", 255, 255, 0, 255)
    engine.log("Script unloaded successfully!", 0, 255, 0, 255)
end

-- ========================================
-- INITIALIZATION
-- ========================================

function initialize_script()
    engine.log("Starting Roblox DataModel Navigation Script...", 0, 255, 255, 255)

    init_gui()
    init_rendering()

    engine.register_on_engine_tick(on_engine_tick)
    engine.register_onunload(on_unload)

    engine.log("Script initialized! Use 'Attach to Roblox' to automatically find all components", 0, 255,
        0, 255)
    engine.log("Hotkeys: F5=Names, F6=Full Info, F7=Debug, F8=Simple, F9=Find Offset, INSERT=Toggle", 0, 255, 0, 255)
end

-- Start the script
initialize_script()
