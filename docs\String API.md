
✂️ Trimming and Padding

    str.trim(s) – Removes leading and trailing whitespace.
    str.ltrim(s) – Removes leading whitespace.
    str.rtrim(s) – Removes trailing whitespace.
    str.pad_left(s, len, char) – Pads the string from the left.
    str.pad_right(s, len, char) – Pads the string from the right.
    str.strip_prefix(s, prefix) – Removes the prefix if present.
    str.strip_suffix(s, suffix) – Removes the suffix if present.



🔍 Search and Matching

    str.startswith(s, prefix) – Returns true if string starts with prefix.
    str.endswith(s, suffix) – Returns true if string ends with suffix.
    str.contains(s, substring) – Returns true if substring is found.
    str.indexof(s, substr, start) – Returns first index of substring (1-based).
    str.last_indexof(s, substr) – Returns last index of substring (1-based).
    str.count(s, substr) – Counts occurrences of substring.
    str.empty(s) – Returns true if string is empty.
    str.equals(a, b) – Compares two strings for equality.



🔧 Modification and Replacement

    str.replace(s, from, to) – Replaces all occurrences of from with to.
    str.repeat_str(s, count) – Repeats string N times.
    str.reverse(s) – Reverses the string.
    str.insert(s, pos, substr) – Inserts substring at position.
    str.remove(s, start, end) – Removes characters from start to end.
    str.substitute(s, table) – Replaces {key} in string with values from a table.



🔡 Case and Splitting

    str.upper(s) – Converts to uppercase.
    str.lower(s) – Converts to lowercase.
    str.split(s, delimiter) – Splits string by delimiter into table.
    str.slice(s, start, end) – Returns a substring (1-based).



🌐 UTF-8 Support

    str.utf8len(s) – Returns number of UTF-8 characters.
    str.utf8sub(s, start, end) – Returns UTF-8-safe substring.

