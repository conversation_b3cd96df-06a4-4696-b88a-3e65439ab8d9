
🏢 Using the Process API

🧷 Process Info & State

    proc.is_attached() – Returns true if a process is currently attached.
    proc.did_exit() – Returns true if the attached process has exited.
    proc.pid() – Returns the process ID of the attached process.
    proc.peb() – Returns the PEB (Process Environment Block) address.
    proc.base_address() – Returns the process's base address.
    proc.get_base_module() – Returns the base module's address and size.
    proc.find_module(module_name) – Finds a module by name and returns its base address and size.
    proc.find_signature(base_address, size, signature) – Searches for a memory pattern within a module.


📖 Read Operations

    proc.read_double(address) – Reads a double from memory.
    proc.read_float(address) – Reads a float from memory.
    proc.read_int64(address) – Reads a 64-bit integer from memory.
    proc.read_int32(address) – Reads a 32-bit integer from memory.
    proc.read_int16(address) – Reads a 16-bit integer from memory.
    proc.read_int8(address) – Reads an 8-bit integer from memory.
    proc.read_string(address, size) – Reads a string from memory.
    proc.read_wide_string(address, size) – Reads a wide (UTF-16) string from memory.
    proc.read_to_memory_buffer(address, buffer, size) – Reads memory into a buffer handle (from m.alloc()).

✍️ Write Operations

    proc.write_double(address, value) – Writes a double to memory.
    proc.write_float(address, value) – Writes a float to memory.
    proc.write_int64(address, value) – Writes a 64-bit integer to memory.
    proc.write_int32(address, value) – Writes a 32-bit integer to memory.
    proc.write_int16(address, value) – Writes a 16-bit integer to memory.
    proc.write_int8(address, value) – Writes an 8-bit integer to memory.
    proc.write_string(address, text) – Writes a string to memory.
    proc.write_wide_string(address, text) – Writes a wide (UTF-16) string to memory.
    proc.write_from_memory_buffer(address, buffer, size) – Writes memory from a buffer handle (from m.alloc()).

This example demonstrates how to interact with a process's memory in Lua.

LUA:
```lua
-- Check if a process is attached
if proc.is_attached() then
    engine.log("Process is attached!", 0, 255, 0, 255)

    -- Get process details
    local process_id = proc.pid()
    local process_base = proc.base_address()
    local main_module, module_size = proc.get_base_module()

    engine.log("Process ID: " .. process_id, 255, 255, 255, 255)
    engine.log("Base Address: " .. process_base, 255, 255, 255, 255)
    engine.log("Main Module: " .. main_module .. " | Size: " .. module_size, 255, 255, 255, 255)

    -- Find a module by name
    local module_base, module_size = proc.find_module("example.dll")
    if module_base ~= nil then
        engine.log("Module 'example.dll' found at: " .. module_base, 0, 255, 0, 255)
    else
        engine.log("Module 'example.dll' not found!", 255, 0, 0, 255)
    end

    -- Read and write process memory
    local address = process_base + 0x1000  -- Example offset
    local original_value = proc.read_int32(address)
    engine.log("Original Value: " .. original_value, 255, 255, 255, 255)

    proc.write_int32(address, 99999)  -- Modify memory value
    engine.log("Modified Value: " .. proc.read_int32(address), 0, 255, 0, 255)

    -- String manipulation
    local str_address = process_base + 0x2000  -- Example string address
    local read_str = proc.read_string(str_address, 20)
    engine.log("Read String: " .. read_str, 255, 255, 255, 255)

    proc.write_string(str_address, "New String")
    engine.log("String Written Successfully!", 0, 255, 0, 255)

    -- Cleanup check
    if proc.did_exit() then
        engine.log("Attached process has exited!", 255, 0, 0, 255)
    end
else
    engine.log("No process is attached!", 255, 0, 0, 255)
end
```

Example Usage:
- Retrieves process ID, base address, and module information.
- Finds a specific module and logs its address.
- Reads an integer from memory, modifies it, and logs the new value.
- Reads and writes a string in process memory.
- Checks if the attached process has exited.

This example ensures safe and efficient interaction with a process's memory in Lua! 🚀🏢
