=== ROBLOX CHARACTER OFFSET SCAN LOG ===
Generated by: Roblox DataModel Navigation Script
Engine time: 1748883776
File: character_offset_scan_1748883776.txt

=== SCAN DATA ===

[SCAN] === CHARACTER OFFSET SCANNING STARTED ===
[SCAN] Engine time: 1748883776
[SCAN] 
[SCAN] Scanning player: ShadowThijs at 0x16B9CAD96B0
[SCAN] Players service address: 0x16BD931E070
[SCAN] Current CHARACTER_OFFSET: 0x1D0
[SCAN] 
[SCAN] Testing current CHARACTER_OFFSET (0x1D0):
[SCAN]   Current offset points to: 0xF
[SCAN]   Invalid address range - too small or too large
[SCAN] 
[SCAN] Scanning for character pointer in player memory (0x100 to 0x400)...
[SCAN] Found Model at offset 0x330: ShadowThijs
[SCAN]   Checking for Humanoid and HumanoidRootPart...
[SCAN]   ✓ Humanoid found at: 0x16B0A6F98A0
[SCAN]   ✗ HumanoidRootPart not found
[SCAN]   ✓ Character name matches player name!
[SCAN]   Final confidence: 3/4 [HIGH CONFIDENCE]
[SCAN] 
[SCAN] === SCAN RESULTS (sorted by confidence) ===
[SCAN] 1. Offset 0x330 -> ShadowThijs (confidence: 3/4)
[SCAN]    Address: 0x16B48F31980
[SCAN]    Has Humanoid: true
[SCAN]    Has HumanoidRootPart: false
[SCAN] 
[SCAN] === RECOMMENDATION ===
[SCAN] RECOMMENDED: Use CHARACTER_OFFSET = 0x330
[SCAN] This offset points to: ShadowThijs with confidence 3/4
[SCAN] 
[SCAN] === POSITION TESTING ===
[SCAN] Testing with character at: 0x16B48F31980
[SCAN] Looking for HumanoidRootPart in character...
[SCAN] ERROR: HumanoidRootPart not found in character
[SCAN] This character may not be a valid player character
[SCAN] 
[SCAN] === SCAN COMPLETE ===
[SCAN] Log saved to: character_offset_scan_1748883776.txt

=== END OF LOG ===