--[[
    ASSUMPTIONS:
    - Your Lua engine provides global functions like:
        Memory.ReadPointer(address)
        Memory.ReadInt(address)
        Memory.ReadFloat(address)
        Memory.ReadBytes(address, count) -- Returns a table of bytes
        -- A function to call an instance's method (property getter)
        -- This is the most complex part and highly engine-specific.
        -- It needs to handle calling conventions (like __thiscall) and return values.
        Memory.CallInstanceGetter_ReturnsPointerToValue(instanceAddress, getterFunctionAddress)

    - You have global variables for key addresses:
        g_PlayersServiceAddress -- Address of the Players service instance
        g_WorkspaceAddress      -- Address of the Workspace service instance
        g_LocalPlayerAddress    -- Address of the LocalPlayer instance (if you want to skip it)

    - You have a global table `g_Offsets` with all necessary offsets:
        g_Offsets.Instance.Name
        g_Offsets.Instance.ChildrenList
        g_Offsets.Instance.ClassDescriptor
        g_Offsets.ChildrenList.Start
        g_Offsets.ChildrenList.End
        g_Offsets.RobloxString.Length
        g_Offsets.RobloxString.DataPointer -- (if applicable for long strings, or just base for short)
        g_Offsets.ClassDescriptor.PropertyDescriptorsListStart
        g_Offsets.ClassDescriptor.PropertyDescriptorsListEnd
        g_Offsets.PropertyDescriptor.NameStringObjectPtr
        g_Offsets.PropertyDescriptor.GetSetImplPtr
        g_Offsets.GetSetImpl.GetFunctionPtr
        g_Offsets.PointerSize -- (e.g., 4 for 32-bit, 8 for 64-bit, for iterating pointer arrays)
--]]

-- Helper function to convert a byte table to a Lua string (very basic)
local function BytesToString(byteTable)
    local str = ""
    if not byteTable then return "" end
    for i = 1, #byteTable do
        if byteTable[i] == 0 then break end -- Stop at null terminator
        str = str .. string.char(byteTable[i])
    end
    return str
end

-- Pseudo-function to read a Roblox string
-- This is simplified; actual Roblox strings have short string optimization.
function GetRobloxString(stringObjectAddress)
    if stringObjectAddress == 0 then return "" end

    local length = Memory.ReadInt(stringObjectAddress + g_Offsets.RobloxString.Length)
    if length == 0 then return "" end
    if length > 1024 then length = 1024 end -- Sanity cap

    local dataAddress
    if length < 16 then -- Assuming Short String Optimization (SSO)
        dataAddress = stringObjectAddress -- Data is within the string object itself
    else
        dataAddress = Memory.ReadPointer(stringObjectAddress) -- Pointer to string data
    end

    if dataAddress == 0 then return "" end
    local bytes = Memory.ReadBytes(dataAddress, length)
    return BytesToString(bytes)
end

-- Pseudo-function to get an instance's name
function GetInstanceName(instanceAddress)
    if instanceAddress == 0 then return "" end
    local nameStringObjectAddress = Memory.ReadPointer(instanceAddress + g_Offsets.Instance.Name) -- Roblox Name property is often a pointer to the string object
    return GetRobloxString(nameStringObjectAddress)
end

-- Pseudo-function to get an array of child instance addresses
function GetChildrenAddresses(parentInstanceAddress)
    local childrenAddresses = {}
    if parentInstanceAddress == 0 then return childrenAddresses end

    local childrenListStructAddress = Memory.ReadPointer(parentInstanceAddress + g_Offsets.Instance.ChildrenList)
    if childrenListStructAddress == 0 then return childrenAddresses end

    local childrenArrayStart = Memory.ReadPointer(childrenListStructAddress + g_Offsets.ChildrenList.Start)
    local childrenArrayEnd = Memory.ReadPointer(childrenListStructAddress + g_Offsets.ChildrenList.End)

    if childrenArrayStart == 0 or childrenArrayEnd == 0 then return childrenAddresses end

    local currentChildPtrLocation = childrenArrayStart
    while currentChildPtrLocation < childrenArrayEnd do
        local childInstanceAddress = Memory.ReadPointer(currentChildPtrLocation)
        if childInstanceAddress ~= 0 then
            table.insert(childrenAddresses, childInstanceAddress)
        end
        currentChildPtrLocation = currentChildPtrLocation + g_Offsets.PointerSize -- Increment by pointer size (e.g., 8 for 64-bit)
    end
    return childrenAddresses
end

-- Pseudo-function to find a direct child's address by its name
function FindFirstChildAddressByName(parentInstanceAddress, targetName)
    local children = GetChildrenAddresses(parentInstanceAddress)
    for _, childAddress in ipairs(children) do
        if GetInstanceName(childAddress) == targetName then
            return childAddress
        end
    end
    return 0 -- Return 0 or nil if not found
end

-- Pseudo-function to get the address of a property's "getter" function
function GetPropertyGetterAddress(instanceAddress, propertyName)
    if instanceAddress == 0 then return 0 end

    local classDescriptorAddress = Memory.ReadPointer(instanceAddress + g_Offsets.Instance.ClassDescriptor)
    if classDescriptorAddress == 0 then return 0 end

    local propListStart = Memory.ReadPointer(classDescriptorAddress + g_Offsets.ClassDescriptor.PropertyDescriptorsListStart)
    local propListEnd = Memory.ReadPointer(classDescriptorAddress + g_Offsets.ClassDescriptor.PropertyDescriptorsListEnd)

    if propListStart == 0 or propListEnd == 0 then return 0 end

    local currentPropDescPtrLocation = propListStart
    -- Property descriptor list is an array of pointers to PropertyDescriptor objects
    while currentPropDescPtrLocation < propListEnd do
        local propDescAddress = Memory.ReadPointer(currentPropDescPtrLocation)
        if propDescAddress ~= 0 then
            local propNamePtr = Memory.ReadPointer(propDescAddress + g_Offsets.PropertyDescriptor.NameStringObjectPtr)
            local currentName = GetRobloxString(propNamePtr)

            if currentName == propertyName then
                local getSetImplAddress = Memory.ReadPointer(propDescAddress + g_Offsets.PropertyDescriptor.GetSetImplPtr)
                if getSetImplAddress ~= 0 then
                    return Memory.ReadPointer(getSetImplAddress + g_Offsets.GetSetImpl.GetFunctionPtr)
                end
                break -- Found name but no GetSetImpl
            end
        end
        currentPropDescPtrLocation = currentPropDescPtrLocation + g_Offsets.PointerSize -- Assuming list of pointers, adjust if it's fixed size entries not matching pointer size
    end
    return 0 -- Getter not found
end


-- Main logic to get other players' positions
print("Starting to fetch other player positions...")

-- 1. Get all Player instances from the Players service
local playerInstanceAddresses = GetChildrenAddresses(g_PlayersServiceAddress)

if #playerInstanceAddresses == 0 then
    print("No players found in Players service.")
    return
end

print("Found " .. #playerInstanceAddresses .. " total player instances.")

for i, playerAddress in ipairs(playerInstanceAddresses) do
    if playerAddress == g_LocalPlayerAddress then
        print("Skipping local player.")
        goto continue -- Lua's way to continue a loop
    end

    local playerName = GetInstanceName(playerAddress)
    if playerName == "" then playerName = "UnknownPlayer_" .. i end
    print("Processing Player: " .. playerName .. " (Address: " .. string.format("0x%X", playerAddress) .. ")")

    -- 2. Get the Character model for this player from Workspace
    -- Player's character in Workspace is usually named the same as the Player.
    local characterAddress = FindFirstChildAddressByName(g_WorkspaceAddress, playerName)

    if characterAddress == 0 then
        print("  Character not found in Workspace for: " .. playerName)
        goto continue
    end
    print("  Found Character: " .. GetInstanceName(characterAddress) .. " (Address: " .. string.format("0x%X", characterAddress) .. ")")

    -- 3. Get the HumanoidRootPart from the Character
    -- Position is most reliably on HumanoidRootPart.
    local humanoidRootPartAddress = FindFirstChildAddressByName(characterAddress, "HumanoidRootPart")

    if humanoidRootPartAddress == 0 then
        print("  HumanoidRootPart not found for: " .. playerName .. ". Position might be on Character model directly or another part.")
        -- As a fallback, you could try to get "Position" from characterAddress itself,
        -- but it's less standard. For this pseudocode, we'll skip if HRP not found.
        goto continue
    end
    print("  Found HumanoidRootPart (Address: " .. string.format("0x%X", humanoidRootPartAddress) .. ")")

    -- 4. Get the "Position" property's getter function for the HumanoidRootPart
    local positionGetterFuncAddress = GetPropertyGetterAddress(humanoidRootPartAddress, "Position")

    if positionGetterFuncAddress == 0 then
        print("  Could not find 'Position' property getter for HumanoidRootPart of: " .. playerName)
        goto continue
    end
    print("  Position Getter Func Address: " .. string.format("0x%X", positionGetterFuncAddress))

    -- 5. Call the getter function.
    -- This is highly dependent on your Lua engine's API for calling member functions.
    -- Assume it returns a pointer to the Vector3 data (X, Y, Z floats).
    local positionVector3DataPtr = Memory.CallInstanceGetter_ReturnsPointerToValue(humanoidRootPartAddress, positionGetterFuncAddress)

    if positionVector3DataPtr == 0 then
        print("  Calling 'Position' getter failed or returned null for: " .. playerName)
        goto continue
    end
    print("  Position Vector3 Data Pointer: " .. string.format("0x%X", positionVector3DataPtr))

    -- 6. Read the X, Y, Z float values from the returned pointer
    local posX = Memory.ReadFloat(positionVector3DataPtr + 0x0) -- X
    local posY = Memory.ReadFloat(positionVector3DataPtr + 0x4) -- Y
    local posZ = Memory.ReadFloat(positionVector3DataPtr + 0x8) -- Z

    print(string.format("  SUCCESS: %s Position: X=%.2f, Y=%.2f, Z=%.2f", playerName, posX, posY, posZ))

    ::continue:: -- Label for goto
end

print("Finished fetching player positions.")

