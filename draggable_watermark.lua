-- Draggable and Customizable Watermark Script
-- This script creates a watermark that can be dragged around the screen and customized through GUI

-- Settings file path
local settings_file = "watermark_settings.json"

-- Initialize watermark state
local watermark = {
    -- Position and size
    x = 640,
    y = 30,
    width = 108,
    height = 32,
    auto_width = true, -- Auto-fit width to content
    base_char_width = 9.5, -- Base character width for calculations
    separator_width = 12.0, -- Width for " | " separators
    padding = 16.0, -- Padding around text
    
    -- Visual properties
    text = "Perception.cx",
    font = nil,
    text_color = {255, 255, 255, 255}, -- RGBA
    rgb_mode = false, -- Enable RGB color cycling
    rgb_speed = 1.0, -- RGB cycling speed
    rgb_time = 0, -- Internal RGB timer
    background_color = {0, 0, 0, 100}, -- RGBA with transparency
    border_color = {255, 255, 255, 255}, -- RGBA
    border_thickness = 2,
    background_enabled = false,
    border_enabled = false,
    rounded_corners = true,
    corner_radius = 5,
    text_shadow = true,
    text_shadow_color = {0, 0, 0, 255}, -- RGBA
    
    -- Content display toggles
    show_username = false,
    show_time = false,
    show_fps = false,
    
    -- Drag state
    is_dragging = false,
    drag_offset_x = 0,
    drag_offset_y = 0,
    
    -- Visibility
    visible = true,
    fade_alpha = 1.0
}

-- GUI state for customization panel
local gui_state = {}

-- Load settings from JSON file
local function load_settings()
    if fs.does_file_exist(settings_file) then
        local content = fs.read_from_file(settings_file)
        if content and content ~= "" then
            local success, settings = pcall(json.parse, content)
            if success and settings then
                -- Apply loaded settings to watermark
                for key, value in pairs(settings) do
                    if watermark[key] ~= nil then
                        watermark[key] = value
                    end
                end
                return true
            end
        end
    end
    return false
end

-- Save settings to JSON file
local function save_settings()
    local settings = {}
    -- Copy all watermark properties except runtime ones
    for key, value in pairs(watermark) do
        if key ~= "font" and key ~= "is_dragging" and key ~= "drag_offset_x" and key ~= "drag_offset_y" and key ~= "rgb_time" then
            settings[key] = value
        end
    end
    
    local success, json_string = pcall(json.stringify, settings)
    if success then
        fs.write_to_file(settings_file, json_string)
        return true
    end
    return false
end

-- Update GUI visibility based on settings
local function update_gui_visibility()
    if not gui_state.panel then return end
      -- Border settings visibility
    if gui_state.border_color then
        gui_state.border_color:set_active(watermark.border_enabled)
    end
    if gui_state.border_thickness then
        gui_state.border_thickness:set_active(watermark.border_enabled)
    end
      -- Background settings visibility
    if gui_state.bg_color then
        gui_state.bg_color:set_active(watermark.background_enabled)
    end
      -- Corner radius visibility (only when rounded corners are enabled)
    if gui_state.corner_radius then
        gui_state.corner_radius:set_active(watermark.rounded_corners)
    end
      -- Width slider visibility (only when auto-width is disabled)
    if gui_state.width_slider then
        gui_state.width_slider:set_active(not watermark.auto_width)
    end
    
    -- RGB speed visibility (only when RGB mode is enabled)    
    if gui_state.rgb_speed then
        gui_state.rgb_speed:set_active(watermark.rgb_mode)
    end
    
    -- All other controls visibility based on main visible toggle
    local main_visible = watermark.visible
    local controls_to_hide = {
        "text_input", "text_color", "show_username", "show_time", "show_fps",
        "height_slider", "auto_width", "rgb_mode", "text_shadow", 
        "bg_enabled", "border_enabled", "rounded_corners", "fade_alpha",
        "pos_x", "pos_y", "reset_button"
    }
    for _, control_name in ipairs(controls_to_hide) do
        if gui_state[control_name] then
            gui_state[control_name]:set_active(main_visible)
        end
    end
end

-- Initialize font
local function init_font()
    watermark.font = render.create_font("cour.ttf", 16) -- Courier New - monospace (forced)
end

-- Update GUI controls to match loaded settings
local function update_gui_from_settings()
    if not gui_state.panel then return end
    
    -- Update all GUI controls with current watermark values
    gui_state.text_input:set(watermark.text)
    gui_state.visible_checkbox:set(watermark.visible)
    gui_state.text_color:set(watermark.text_color[1], watermark.text_color[2], watermark.text_color[3], watermark.text_color[4])
    
    gui_state.show_username:set(watermark.show_username)
    gui_state.show_time:set(watermark.show_time)
    gui_state.show_fps:set(watermark.show_fps)
    
    gui_state.width_slider:set(watermark.width)
    gui_state.height_slider:set(watermark.height)
    gui_state.auto_width:set(watermark.auto_width)
    
    gui_state.rgb_mode:set(watermark.rgb_mode)
    gui_state.rgb_speed:set(watermark.rgb_speed)
    gui_state.text_shadow:set(watermark.text_shadow)
    
    gui_state.bg_enabled:set(watermark.background_enabled)
    gui_state.bg_color:set(watermark.background_color[1], watermark.background_color[2], watermark.background_color[3], watermark.background_color[4])
    
    gui_state.border_enabled:set(watermark.border_enabled)
    gui_state.border_color:set(watermark.border_color[1], watermark.border_color[2], watermark.border_color[3], watermark.border_color[4])
    gui_state.border_thickness:set(watermark.border_thickness)
    
    gui_state.rounded_corners:set(watermark.rounded_corners)
    gui_state.corner_radius:set(watermark.corner_radius)
    gui_state.fade_alpha:set(watermark.fade_alpha)
    
    gui_state.pos_x:set(watermark.x)
    gui_state.pos_y:set(watermark.y)
    
    -- Update visibility after setting values
    update_gui_visibility()
end

-- Initialize GUI
local function init_gui()
    local tab = gui.get_tab("settings")
    gui_state.panel = tab:create_panel("Watermark Settings", false)
    
    -- Text settings
    gui_state.text_input = gui_state.panel:add_input_text("Watermark Text", watermark.text)
    gui_state.visible_checkbox = gui_state.panel:add_checkbox("Visible")
    gui_state.visible_checkbox:set(watermark.visible)    gui_state.text_color = gui_state.panel:add_color_picker("Text Color", 
        watermark.text_color[1], watermark.text_color[2], watermark.text_color[3], watermark.text_color[4])
    
    -- Content display toggles
    gui_state.show_username = gui_state.panel:add_checkbox("Show Username")
    gui_state.show_username:set(watermark.show_username)
    
    gui_state.show_time = gui_state.panel:add_checkbox("Show Time")
    gui_state.show_time:set(watermark.show_time)
    
    gui_state.show_fps = gui_state.panel:add_checkbox("Show FPS")
    gui_state.show_fps:set(watermark.show_fps)
    
    -- Size settings
    gui_state.width_slider = gui_state.panel:add_slider_int("Width", 50, 800, watermark.width)
    gui_state.height_slider = gui_state.panel:add_slider_int("Height", 20, 200, watermark.height)
    gui_state.auto_width = gui_state.panel:add_checkbox("Auto-fit Width")
    gui_state.auto_width:set(watermark.auto_width)
    
    -- Color settings
    gui_state.rgb_mode = gui_state.panel:add_checkbox("RGB Mode (Auto Color Cycle)")
    gui_state.rgb_mode:set(watermark.rgb_mode)
    
    gui_state.rgb_speed = gui_state.panel:add_slider_float("RGB Speed", 0.1, 5.0, watermark.rgb_speed)
    
    gui_state.text_shadow = gui_state.panel:add_checkbox("Text Shadow")
    gui_state.text_shadow:set(watermark.text_shadow)
      gui_state.bg_enabled = gui_state.panel:add_checkbox("Background Enabled")
    gui_state.bg_enabled:set(watermark.background_enabled)
    
    gui_state.bg_color = gui_state.panel:add_color_picker("Background Color",
        watermark.background_color[1], watermark.background_color[2], watermark.background_color[3], watermark.background_color[4])
    
    gui_state.border_enabled = gui_state.panel:add_checkbox("Border Enabled")
    gui_state.border_enabled:set(watermark.border_enabled)
    
    gui_state.border_color = gui_state.panel:add_color_picker("Border Color",
        watermark.border_color[1], watermark.border_color[2], watermark.border_color[3], watermark.border_color[4])
    
    gui_state.border_thickness = gui_state.panel:add_slider_int("Border Thickness", 1, 10, watermark.border_thickness)
    
    -- Style settings
    gui_state.rounded_corners = gui_state.panel:add_checkbox("Rounded Corners")
    gui_state.rounded_corners:set(watermark.rounded_corners)
    
    gui_state.corner_radius = gui_state.panel:add_slider_int("Corner Radius", 0, 20, watermark.corner_radius)
    
    gui_state.fade_alpha = gui_state.panel:add_slider_float("Fade Alpha", 0.0, 1.0, watermark.fade_alpha)
      -- Position controls
    gui_state.pos_x = gui_state.panel:add_slider_int("Position X", 0, 1920, watermark.x)
    gui_state.pos_y = gui_state.panel:add_slider_int("Position Y", 0, 1080, watermark.y)
      -- Reset button
    gui_state.reset_button = gui_state.panel:add_button("Reset Position", function()
        watermark.x = 50
        watermark.y = 50
        gui_state.pos_x:set(watermark.x)
        gui_state.pos_y:set(watermark.y)
    end)
      -- Save/Load buttons
    gui_state.save_button = gui_state.panel:add_button("Save Settings", function()
        if save_settings() then
            engine.log("Settings saved successfully!", 0, 255, 0, 255)
        else
            engine.log("Failed to save settings!", 255, 0, 0, 255)
        end
    end)
end

-- Update watermark properties from GUI
local function update_from_gui()
    if not gui_state.panel then return end
    
    local old_visible = watermark.visible
    local old_border_enabled = watermark.border_enabled
    local old_background_enabled = watermark.background_enabled
    local old_rounded_corners = watermark.rounded_corners
    local old_auto_width = watermark.auto_width
    local old_rgb_mode = watermark.rgb_mode
    
    watermark.text = gui_state.text_input:get()
    watermark.visible = gui_state.visible_checkbox:get()
    
    -- Content display toggles
    watermark.show_username = gui_state.show_username:get()
    watermark.show_time = gui_state.show_time:get()
    watermark.show_fps = gui_state.show_fps:get()
    
    -- Size settings
    watermark.auto_width = gui_state.auto_width:get()
    if not watermark.auto_width then
        watermark.width = gui_state.width_slider:get()
    end
    watermark.height = gui_state.height_slider:get()
    
    -- Update colors
    watermark.text_color[1], watermark.text_color[2], watermark.text_color[3], watermark.text_color[4] = gui_state.text_color:get()
    watermark.rgb_mode = gui_state.rgb_mode:get()
    watermark.rgb_speed = gui_state.rgb_speed:get()
    watermark.background_color[1], watermark.background_color[2], watermark.background_color[3], watermark.background_color[4] = gui_state.bg_color:get()
    watermark.border_color[1], watermark.border_color[2], watermark.border_color[3], watermark.border_color[4] = gui_state.border_color:get()
    
    -- Update properties
    watermark.background_enabled = gui_state.bg_enabled:get()
    watermark.border_enabled = gui_state.border_enabled:get()
    watermark.border_thickness = gui_state.border_thickness:get()
    watermark.rounded_corners = gui_state.rounded_corners:get()
    watermark.corner_radius = gui_state.corner_radius:get()
    watermark.fade_alpha = gui_state.fade_alpha:get()
    watermark.text_shadow = gui_state.text_shadow:get()
    
    -- Update position from GUI if not dragging
    if not watermark.is_dragging then
        watermark.x = gui_state.pos_x:get()
        watermark.y = gui_state.pos_y:get()
    else
        -- Update GUI sliders to match dragged position
        gui_state.pos_x:set(watermark.x)
        gui_state.pos_y:set(watermark.y)
    end
    
    -- Update GUI visibility if any relevant setting changed
    if old_visible ~= watermark.visible or 
       old_border_enabled ~= watermark.border_enabled or
       old_background_enabled ~= watermark.background_enabled or
       old_rounded_corners ~= watermark.rounded_corners or
       old_auto_width ~= watermark.auto_width or
       old_rgb_mode ~= watermark.rgb_mode then
        update_gui_visibility()
    end
end

-- Check if mouse is over watermark
local function is_mouse_over_watermark()
    local mouse_x, mouse_y = input.get_mouse_position()
    return mouse_x >= watermark.x and mouse_x <= watermark.x + watermark.width and
           mouse_y >= watermark.y and mouse_y <= watermark.y + watermark.height
end

-- Handle dragging logic
local function handle_dragging()
    local mouse_x, mouse_y = input.get_mouse_position()
    
    -- Start dragging
    if input.is_key_pressed(1) and is_mouse_over_watermark() and not watermark.is_dragging then -- Left mouse button
        watermark.is_dragging = true
        watermark.drag_offset_x = mouse_x - watermark.x
        watermark.drag_offset_y = mouse_y - watermark.y
    end
    
    -- Update position while dragging
    if watermark.is_dragging and input.is_key_down(1) then
        watermark.x = mouse_x - watermark.drag_offset_x
        watermark.y = mouse_y - watermark.drag_offset_y
        
        -- Clamp to screen bounds
        local screen_width, screen_height = render.get_viewport_size()
        watermark.x = math.max(0, math.min(watermark.x, screen_width - watermark.width))
        watermark.y = math.max(0, math.min(watermark.y, screen_height - watermark.height))
    end
    
    -- Stop dragging
    if watermark.is_dragging and not input.is_key_down(1) then
        watermark.is_dragging = false
    end
end

-- Apply alpha fade to colors
local function apply_alpha_fade(color)
    return {
        color[1],
        color[2], 
        color[3],
        math.floor(color[4] * watermark.fade_alpha)
    }
end

-- Get current time formatted as HH:MM:SS
local function get_current_time()
    return time.now_local():match("%d+:%d+:%d+") or time.now_local()
end

-- Calculate RGB color based on time
local function calculate_rgb_color()
    watermark.rgb_time = watermark.rgb_time + (0.016 * watermark.rgb_speed) -- Approximate 60 FPS delta time
    local r = math.floor((math.sin(watermark.rgb_time) * 127) + 128)
    local g = math.floor((math.sin(watermark.rgb_time + 2.09) * 127) + 128) -- 2π/3 offset
    local b = math.floor((math.sin(watermark.rgb_time + 4.19) * 127) + 128) -- 4π/3 offset
    return {r, g, b, watermark.text_color[4]} -- Keep original alpha
end

-- Build the display text based on enabled components
local function build_display_text()
    local text_parts = {}
    
    -- Add custom text if not empty
    local custom_text = watermark.text
    if custom_text == "" then
        custom_text = " " -- Use space to prevent empty string error
    end
    table.insert(text_parts, custom_text)
    
    -- Add username if enabled
    if watermark.show_username then
        local username = engine.get_username()
        if username and username ~= "" then
            table.insert(text_parts, "" .. username)
        end
    end
    
    -- Add time if enabled
    if watermark.show_time then
        table.insert(text_parts, "" .. get_current_time())
    end
    
    -- Add FPS if enabled
    if watermark.show_fps then
        local fps = render.get_fps()
        if fps then
            table.insert(text_parts, "" .. math.floor(fps))
        end
    end
    
    return table.concat(text_parts, " | ")
end

-- Calculate auto-fit width based on text content (fixed for monospace)
local function calculate_auto_width(text)
    -- Fixed parameters optimized for monospace font
    local base_char_width = watermark.base_char_width
    local separator_width = watermark.separator_width
    local padding = watermark.padding
    
    -- Count separators first
    local separator_count = 0
    local temp_text = text
    while temp_text:find(" | ") do
        separator_count = separator_count + 1
        temp_text = temp_text:gsub(" | ", "", 1)
    end
    
    -- Remove separators and count remaining characters
    local clean_text = text:gsub(" | ", "")
    local char_count = #clean_text
    
    -- Calculate width: characters + separators + padding
    local estimated_width = math.floor((char_count * base_char_width) + 
                           (separator_count * separator_width) + 
                           padding)
    
    return math.max(60, estimated_width) -- Minimum width
end

-- Render the watermark
local function render_watermark()
    if not watermark.visible or not watermark.font then return end
    
    -- Build the complete display text
    local display_text = build_display_text()
    
    -- Auto-calculate width if enabled
    if watermark.auto_width then
        watermark.width = calculate_auto_width(display_text)
    end
      -- Apply fade to all colors
    local text_color = watermark.rgb_mode and calculate_rgb_color() or watermark.text_color
    local faded_text_color = apply_alpha_fade(text_color)
    local faded_bg_color = apply_alpha_fade(watermark.background_color)
    local faded_border_color = apply_alpha_fade(watermark.border_color)
    local faded_shadow_color = apply_alpha_fade(watermark.text_shadow_color)
    
    -- Draw background
    if watermark.background_enabled then
        if watermark.rounded_corners then
            render.draw_rectangle(
                watermark.x, watermark.y, watermark.width, watermark.height,
                faded_bg_color[1], faded_bg_color[2], faded_bg_color[3], faded_bg_color[4],
                0, true, watermark.corner_radius
            )
        else
            render.draw_rectangle(
                watermark.x, watermark.y, watermark.width, watermark.height,
                faded_bg_color[1], faded_bg_color[2], faded_bg_color[3], faded_bg_color[4],
                0, true, 0
            )
        end
    end
    
    -- Draw border
    if watermark.border_enabled then
        if watermark.rounded_corners then
            render.draw_rectangle(
                watermark.x, watermark.y, watermark.width, watermark.height,
                faded_border_color[1], faded_border_color[2], faded_border_color[3], faded_border_color[4],
                watermark.border_thickness, false, watermark.corner_radius
            )
        else
            render.draw_rectangle(
                watermark.x, watermark.y, watermark.width, watermark.height,
                faded_border_color[1], faded_border_color[2], faded_border_color[3], faded_border_color[4],
                watermark.border_thickness, false, 0
            )
        end
    end
    
    -- Calculate text position (centered vertically, with left padding)
    local text_x = watermark.x + 10 -- Small padding from left
    local text_y = watermark.y + (watermark.height / 2) - 8 -- Approximate center
    
    -- Draw text shadow if enabled
    if watermark.text_shadow then
        render.draw_text(
            watermark.font,
            display_text,
            text_x + 1, text_y + 1, -- Offset for shadow
            faded_shadow_color[1], faded_shadow_color[2], faded_shadow_color[3], faded_shadow_color[4],
            0, -- no outline for shadow
            0, 0, 0, 0
        )
    end
    
    -- Draw main text
    if watermark.text_shadow then
        -- No outline when shadow is enabled
        render.draw_text(
            watermark.font,
            display_text,
            text_x, text_y,
            faded_text_color[1], faded_text_color[2], faded_text_color[3], faded_text_color[4],
            0, -- no outline thickness
            0, 0, 0, 0
        )
    else
        -- Outline when shadow is disabled
        render.draw_text(
            watermark.font,
            display_text,
            text_x, text_y,
            faded_text_color[1], faded_text_color[2], faded_text_color[3], faded_text_color[4],
            1, -- outline thickness
            0, 0, 0, 255 -- black outline
        )
    end
    
    -- Draw drag indicator when being dragged
    if watermark.is_dragging then
        render.draw_rectangle(
            watermark.x - 2, watermark.y - 2, watermark.width + 4, watermark.height + 4,
            255, 255, 0, 100, -- Yellow highlight
            2, false, watermark.rounded_corners and watermark.corner_radius or 0
        )
    end
    
    -- Draw hover indicator
    if is_mouse_over_watermark() and not watermark.is_dragging then
        render.draw_rectangle(
            watermark.x - 1, watermark.y - 1, watermark.width + 2, watermark.height + 2,
            255, 255, 255, 50, -- White highlight
            1, false, watermark.rounded_corners and watermark.corner_radius or 0
        )
    end
end

-- Main update function called every engine tick
local function on_engine_tick()
    update_from_gui()
    handle_dragging()
    render_watermark()
end

-- Cleanup function
local function on_unload()
    -- Auto-save settings on unload
    save_settings()
end

-- Initialize everything
local function initialize()
    init_font()
    
    -- Try to load settings first
    if not load_settings() then
        -- Create initial settings file if it doesn't exist
        save_settings()
        engine.log("Created initial settings file", 0, 255, 0, 255)
    else
        engine.log("Settings loaded successfully", 0, 255, 0, 255)
    end
      init_gui()
    
    -- Update GUI controls to match loaded settings
    update_gui_from_settings()
    
    engine.log("Draggable watermark initialized! Use the Settings tab to customize.", 0, 255, 0, 255)
end

-- Register callbacks
engine.register_on_engine_tick(on_engine_tick)
engine.register_onunload(on_unload)

-- Initialize the watermark system
initialize()
