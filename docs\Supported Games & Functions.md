# Supported Games & Functions

Standardized access to cross-game runtime data and features.

# Fortnite
fortnite.get_player_name

Signature: fortnite.get_player_name(address)

Description: Retrieves the decrypted player name from a known memory address.

Parameters:

    address (integer): The memory address pointing to the name structure

Returns:

    string: Decrypted player name

Example:

```lua
local name = fortnite.get_player_name(address)
engine.log("Player name: " .. name, 0, 255, 0, 255)
```

# Rust
rust.get_transform_position

Signature: rust.get_transform_position(address)

Description: Retrieves world-space transform position from a transform address.

Parameters:

    address (integer): Pointer to the transform component

Returns:

    x (number): X coordinate in world space

    y (number): Y coordinate in world space

    z (number): Z coordinate in world space

Example:

```lua
local x, y, z = rust.get_transform_position(address)
engine.log("Transform position: (" .. x .. ", " .. y .. ", " .. z .. ")", 0, 255, 0, 255)
```

# PUBG

The init_decrypt function for PUBG may have been a bait set by PUBG developers or anti-cheat. Do NOT use this function in production or live environments. We are currently testing the function (or just using the universal API loaded on PUBG) to see how it behaves. Use at your own risk.
pubg.init_decrypt

Signature: pubg.init_decrypt(offset)

Description: Initializes the decryption system with a given offset.

Parameters:

    offset (integer): Integer offset required for decryption initialization

Returns:

    boolean: true if successful, false otherwise

pubg.xe_decrypt

Signature: pubg.xe_decrypt(address)

Description: Decrypts a given encrypted memory address.

Parameters:

    address (integer): Encrypted memory address as an integer

Returns:

    integer: Decrypted memory address