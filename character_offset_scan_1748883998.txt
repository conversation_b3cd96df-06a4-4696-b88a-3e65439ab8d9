=== ROBLOX CHARACTER OFFSET SCAN LOG ===
Generated by: Roblox DataModel Navigation Script
Engine time: 1748883998
File: character_offset_scan_1748883998.txt

=== SCAN DATA ===

[SCAN] === CHARACTER OFFSET SCANNING STARTED ===
[SCAN] Engine time: 1748883998
[SCAN] 
[SCAN] Scanning player: <PERSON><PERSON>hijs at 0x16B9CAD96B0
[SCAN] Players service address: 0x16BD931E070
[SCAN] Current CHARACTER_OFFSET: 0x1D0
[SCAN] 
[SCAN] Testing current CHARACTER_OFFSET (0x1D0):
[SCAN]   Current offset points to: 0xF
[SCAN]   Invalid address range - too small or too large
[SCAN] 
[SCAN] Scanning for character pointer in player memory (0x100 to 0x400)...
[SCAN] Found Model at offset 0x330: ShadowThijs
[SCAN]   Checking for Humanoid and HumanoidRootPart...
[SCAN]   --- Searching for Humanoid ---
[SCAN] Searching for 'Humanoid' in parent 0x16B48F31980
[SCAN] Children list object at: 0x16B9AAF3998
[SCAN] Scanning children from 0x16B0C545240 to 0x16B0C5453B0
[SCAN]   Child 1: Humanoid (Humanoid) at 0x16B0A6F98A0
[SCAN] Found target 'Humanoid' at 0x16B0A6F98A0
[SCAN]   --- Searching for HumanoidRootPart ---
[SCAN] Searching for 'HumanoidRootPart' in parent 0x16B48F31980
[SCAN] Children list object at: 0x16B9AAF3998
[SCAN] Scanning children from 0x16B0C545240 to 0x16B0C5453B0
[SCAN]   Child 1: Humanoid (Humanoid) at 0x16B0A6F98A0
[SCAN]   Child 2: Failed to read name (��w��) at 0x16B13D21830
[SCAN]   Child 3: ��Yk (Folder) at 0x16BD462FD20
[SCAN]   Child 4: �z��� (h���) at 0x16B13D120B0
[SCAN]   Child 5: Weapon (Folder) at 0x16BD462FB40
[SCAN]   Child 6: ���� (h���) at 0x16B13D11C90
[SCAN]   Child 7: Animate (LocalScript) at 0x16B82AB27B0
[SCAN]   Child 8: Failed to read name (�����) at 0x16B13D12500
[SCAN]   Child 9: Mounts (Folder) at 0x16BD4630860
[SCAN]   Child 10: hN��� (h���) at 0x16B13D11BD0
[SCAN]   Child 11: Avatar (Folder) at 0x16BD462F3C0
[SCAN]   Child 12: h��� (h���) at 0x16B13D12050
[SCAN]   Child 13: Health (Script) at 0x16B82AB29A0
[SCAN]   Child 14: @C��� (H����) at 0x16B13D11B40
[SCAN]   Child 15: Body Colors (BodyColors) at 0x16BD9F9A260
[SCAN]   Child 16: h��� (�����) at 0x16B13D11B70
[SCAN]   Child 17: CharacterMesh (CharacterMesh) at 0x16BD5461EA0
[SCAN]   Child 18: �z��� (�n{��) at 0x16B13D11C00
[SCAN]   Child 19: CharacterMesh (CharacterMesh) at 0x16BD5464810
[SCAN]   Child 20: p��� (�n{��) at 0x16B13D11FC0
[SCAN]   Child 21: CharacterMesh (CharacterMesh) at 0x16BD5464920
[SCAN]   Child 22: hN��� (�n{��) at 0x16B13D12290
[SCAN]   Child 23: CharacterMesh (CharacterMesh) at 0x16BD5462C70
[SCAN]   Child 24:  (�n{��) at 0x16B13D125C0
[SCAN]   Child 25: CharacterMesh (CharacterMesh) at 0x16BD54645F0
[SCAN]   Child 26: ���� (�n{��) at 0x16B13D12080
[SCAN]   Child 27: Hat1 (Accessory) at 0x16B82AB04D0
[SCAN]   Child 28: hN��� (�b}��) at 0x16B13D11EA0
[SCAN]   Child 29: VarietyShades02 (Accessory) at 0x16B82AB1450
[SCAN]   Child 30: ����� (�b}��) at 0x16B13D11CF0
[SCAN]   Child 31: Torso (Part) at 0x16BBD7DF450
[SCAN]   Child 32: ���� (@c{��) at 0x16B13D122C0
[SCAN]   Child 33: Left Arm (Part) at 0x16BBD7DB4E0
[SCAN]   Child 34: Failed to read name (@c{��) at 0x16B13D12710
[SCAN]   Child 35: Head (Part) at 0x16BBD7DF680
[SCAN]   Child 36:  (@c{��) at 0x16B13D12CE0
[SCAN]   Child 37: Right Arm (Part) at 0x16BBD7DB710
[SCAN]   Child 38: Failed to read name (@c{��) at 0x16B13D13CA0
[SCAN]   Child 39: ��=k (Part) at 0x16BBD7DF8B0
[SCAN]   Child 40: Failed to read name (@c{��) at 0x16B13D13AF0
[SCAN]   Child 41: Right Leg (Part) at 0x16BBD7DFAE0
[SCAN]   Child 42: Failed to read name (@c{��) at 0x16B13D14300
[SCAN]   Child 43: Left Leg (Part) at 0x16BBD7E2B00
[SCAN]   Child 44: Failed to read name (@c{��) at 0x16B13D14480
[SCAN]   Child 45: Gyro (ObjectValue) at 0x16B63DF4100
[SCAN]   Child 46:  (`�}��) at 0x16B59E07B60
[SCAN] Target 'HumanoidRootPart' not found among 46 children
[SCAN]   ✓ Humanoid found at: 0x16B0A6F98A0
[SCAN]   ✗ HumanoidRootPart not found
[SCAN]   ✓ Character name matches player name!
[SCAN]   Final confidence: 3/4 [HIGH CONFIDENCE]
[SCAN] 
[SCAN] === SCAN RESULTS (sorted by confidence) ===
[SCAN] 1. Offset 0x330 -> ShadowThijs (confidence: 3/4)
[SCAN]    Address: 0x16B48F31980
[SCAN]    Has Humanoid: true
[SCAN]    Has HumanoidRootPart: false
[SCAN] 
[SCAN] === RECOMMENDATION ===
[SCAN] RECOMMENDED: Use CHARACTER_OFFSET = 0x330
[SCAN] This offset points to: ShadowThijs with confidence 3/4
[SCAN] 
[SCAN] === POSITION TESTING ===
[SCAN] Testing with character at: 0x16B48F31980
[SCAN] Looking for HumanoidRootPart in character...
[SCAN] --- Detailed HumanoidRootPart Search ---
[SCAN] Searching for 'HumanoidRootPart' in parent 0x16B48F31980
[SCAN] Children list object at: 0x16B9AAF3998
[SCAN] Scanning children from 0x16B0C545240 to 0x16B0C5453B0
[SCAN]   Child 1: Humanoid (Humanoid) at 0x16B0A6F98A0
[SCAN]   Child 2: Failed to read name (��w��) at 0x16B13D21830
[SCAN]   Child 3: ��Yk (Folder) at 0x16BD462FD20
[SCAN]   Child 4: �z��� (h���) at 0x16B13D120B0
[SCAN]   Child 5: Weapon (Folder) at 0x16BD462FB40
[SCAN]   Child 6: ���� (h���) at 0x16B13D11C90
[SCAN]   Child 7: Animate (LocalScript) at 0x16B82AB27B0
[SCAN]   Child 8: Failed to read name (�����) at 0x16B13D12500
[SCAN]   Child 9: Mounts (Folder) at 0x16BD4630860
[SCAN]   Child 10: hN��� (h���) at 0x16B13D11BD0
[SCAN]   Child 11: Avatar (Folder) at 0x16BD462F3C0
[SCAN]   Child 12: h��� (h���) at 0x16B13D12050
[SCAN]   Child 13: Health (Script) at 0x16B82AB29A0
[SCAN]   Child 14: @C��� (H����) at 0x16B13D11B40
[SCAN]   Child 15: Body Colors (BodyColors) at 0x16BD9F9A260
[SCAN]   Child 16: h��� (�����) at 0x16B13D11B70
[SCAN]   Child 17: CharacterMesh (CharacterMesh) at 0x16BD5461EA0
[SCAN]   Child 18: �z��� (�n{��) at 0x16B13D11C00
[SCAN]   Child 19: CharacterMesh (CharacterMesh) at 0x16BD5464810
[SCAN]   Child 20: p��� (�n{��) at 0x16B13D11FC0
[SCAN]   Child 21: CharacterMesh (CharacterMesh) at 0x16BD5464920
[SCAN]   Child 22: hN��� (�n{��) at 0x16B13D12290
[SCAN]   Child 23: CharacterMesh (CharacterMesh) at 0x16BD5462C70
[SCAN]   Child 24:  (�n{��) at 0x16B13D125C0
[SCAN]   Child 25: CharacterMesh (CharacterMesh) at 0x16BD54645F0
[SCAN]   Child 26: ���� (�n{��) at 0x16B13D12080
[SCAN]   Child 27: Hat1 (Accessory) at 0x16B82AB04D0
[SCAN]   Child 28: hN��� (�b}��) at 0x16B13D11EA0
[SCAN]   Child 29: VarietyShades02 (Accessory) at 0x16B82AB1450
[SCAN]   Child 30: ����� (�b}��) at 0x16B13D11CF0
[SCAN]   Child 31: Torso (Part) at 0x16BBD7DF450
[SCAN]   Child 32: ���� (@c{��) at 0x16B13D122C0
[SCAN]   Child 33: Left Arm (Part) at 0x16BBD7DB4E0
[SCAN]   Child 34: Failed to read name (@c{��) at 0x16B13D12710
[SCAN]   Child 35: Head (Part) at 0x16BBD7DF680
[SCAN]   Child 36:  (@c{��) at 0x16B13D12CE0
[SCAN]   Child 37: Right Arm (Part) at 0x16BBD7DB710
[SCAN]   Child 38: Failed to read name (@c{��) at 0x16B13D13CA0
[SCAN]   Child 39: ��=k (Part) at 0x16BBD7DF8B0
[SCAN]   Child 40: Failed to read name (@c{��) at 0x16B13D13AF0
[SCAN]   Child 41: Right Leg (Part) at 0x16BBD7DFAE0
[SCAN]   Child 42: Failed to read name (@c{��) at 0x16B13D14300
[SCAN]   Child 43: Left Leg (Part) at 0x16BBD7E2B00
[SCAN]   Child 44: Failed to read name (@c{��) at 0x16B13D14480
[SCAN]   Child 45: Gyro (ObjectValue) at 0x16B63DF4100
[SCAN]   Child 46:  (`�}��) at 0x16B59E07B60
[SCAN] Target 'HumanoidRootPart' not found among 46 children
[SCAN] --- End HumanoidRootPart Search ---
[SCAN] ERROR: HumanoidRootPart not found in character
[SCAN] This character may not be a valid player character
[SCAN] 
[SCAN] === SCAN COMPLETE ===
[SCAN] Log saved to: character_offset_scan_1748883998.txt

=== END OF LOG ===