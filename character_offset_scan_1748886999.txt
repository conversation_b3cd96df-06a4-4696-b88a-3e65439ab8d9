=== ROBLOX CHARACTER OFFSET SCAN LOG ===
Generated by: Roblox DataModel Navigation Script
Engine time: 1748886999
File: character_offset_scan_1748886999.txt

=== SCAN DATA ===

[SCAN] === CHARACTER OFFSET SCANNING STARTED ===
[SCAN] Engine time: 1748886999
[SCAN] 
[SCAN] Scanning player: <PERSON><PERSON>hi<PERSON><PERSON> at 0x16B9CAD7A90
[SCAN] Players service address: 0x16BA4E01320
[SCAN] Current CHARACTER_OFFSET: 0x1D0
[SCAN] 
[SCAN] Testing current CHARACTER_OFFSET (0x1D0):
[SCAN]   Current offset points to: 0xF
[SCAN]   Invalid address range - too small or too large
[SCAN] 
[SCAN] Scanning for character pointer in player memory (0x100 to 0x400)...
[SCAN] Found Model at offset 0x330: ShadowThijs
[SCAN]   Checking for Humanoid and HumanoidRootPart...
[SCAN]   --- Searching for Humanoid ---
[SCAN] Searching for 'Humanoid' in parent 0x16B653136E0
[SCAN] Children list object at: 0x16B0C44DF38
[SCAN] Scanning children from 0x16BB52E7570 to 0x16BB52E76E0
[SCAN]   Child 1: Humanoid (Humanoid) at 0x16BB1150CB0
[SCAN] Found target 'Humanoid' at 0x16BB1150CB0
[SCAN]   ✓ Humanoid found at: 0x16BB1150CB0
[SCAN]   --- Searching for HumanoidRootPart inside Humanoid ---
[SCAN] Searching for 'HumanoidRootPart' in parent 0x16BB1150CB0
[SCAN] Children list object at: 0x16B0C44DA38
[SCAN] Scanning children from 0x16B0C4537A0 to 0x16B0C4537D0
[SCAN]   Child 1: Animator (Animator) at 0x16B9E9CC720
[SCAN]   Child 2: �z��� (�����) at 0x16B9E498120
[SCAN]   Child 3:  �ak (�
4k) at 0x16BA8AB1BC0
[SCAN]   Child 4: 0��� (�|���) at 0x16B9E4986F0
[SCAN]   Child 5: Status (Status) at 0x16B653133E0
[SCAN]   Child 6: ����� (0����) at 0x16B9E489BA0
[SCAN] Target 'HumanoidRootPart' not found among 6 children
[SCAN]   ✗ HumanoidRootPart not found inside Humanoid
[SCAN]   --- Fallback: Searching for HumanoidRootPart directly in character ---
[SCAN] Searching for 'HumanoidRootPart' in parent 0x16B653136E0
[SCAN] Children list object at: 0x16B0C44DF38
[SCAN] Scanning children from 0x16BB52E7570 to 0x16BB52E76E0
[SCAN]   Child 1: Humanoid (Humanoid) at 0x16BB1150CB0
[SCAN]   Child 2: Failed to read name (��w��) at 0x16B9E498810
[SCAN]   Child 3: PM�k (Folder) at 0x16BBA9EDCA0
[SCAN]   Child 4: Failed to read name (h���) at 0x16B9E498450
[SCAN]   Child 5: Weapon (Folder) at 0x16BBA9EE150
[SCAN]   Child 6: �z��� (h���) at 0x16B9E498840
[SCAN]   Child 7: Animate (LocalScript) at 0x16B5ACF9910
[SCAN]   Child 8: Failed to read name (�����) at 0x16B9E4988A0
[SCAN]   Child 9: Mounts (Folder) at 0x16BBA9EE240
[SCAN]   Child 10: h��� (h���) at 0x16B9E499800
[SCAN]   Child 11: Avatar (Folder) at 0x16BBA9EF140
[SCAN]   Child 12: Failed to read name (h���) at 0x16B9E499860
[SCAN]   Child 13: Health (Script) at 0x16B5ACF7820
[SCAN]   Child 14: ���� (H����) at 0x16B9E498F90
[SCAN]   Child 15: Body Colors (BodyColors) at 0x16B91AF6D10
[SCAN]   Child 16: Failed to read name (�����) at 0x16B9E499710
[SCAN]   Child 17: CharacterMesh (CharacterMesh) at 0x16BDE3FA460
[SCAN]   Child 18: �$��� (�n{��) at 0x16B9E499380
[SCAN]   Child 19: CharacterMesh (CharacterMesh) at 0x16BDE3FA570
[SCAN]   Child 20: ���� (�n{��) at 0x16B9E499620
[SCAN]   Child 21: CharacterMesh (CharacterMesh) at 0x16BDE3FCAA0
[SCAN]   Child 22: Failed to read name (�n{��) at 0x16B9E499260
[SCAN]   Child 23: CharacterMesh (CharacterMesh) at 0x16BDE3FC440
[SCAN]   Child 24: Failed to read name (�n{��) at 0x16B9E498E10
[SCAN]   Child 25: CharacterMesh (CharacterMesh) at 0x16BDE3FCCC0
[SCAN]   Child 26: ���� (�n{��) at 0x16B9E498E40
[SCAN]   Child 27: Hat1 (Accessory) at 0x16B5ACF7C00
[SCAN]   Child 28: @���� (�b}��) at 0x16B9E4995C0
[SCAN]   Child 29: VarietyShades02 (Accessory) at 0x16B5ACF85B0
[SCAN]   Child 30: ���� (�b}��) at 0x16B9E4995F0
[SCAN]   Child 31: Left Leg (Part) at 0x16B91B23FF0
[SCAN]   Child 32: Failed to read name (@c{��) at 0x16B9E499500
[SCAN]   Child 33: ��=k (Part) at 0x16B91B232D0
[SCAN]   Child 34: Failed to read name (@c{��) at 0x16B9E499C20
[SCAN]   Child 35: Right Leg (Part) at 0x16B91B22C40
[SCAN]   Child 36: Failed to read name (@c{��) at 0x16B9E489600
[SCAN]   Child 37: Right Arm (Part) at 0x16B91B230A0
[SCAN]   Child 38: ����� (@c{��) at 0x16B9E489C00
[SCAN]   Child 39: Left Arm (Part) at 0x16B91B22E70
[SCAN]   Child 40:  (@c{��) at 0x16B9E48A140
[SCAN]   Child 41: Torso (Part) at 0x16B91B23500
[SCAN]   Child 42: ���� (@c{��) at 0x16B9E48A8C0
[SCAN]   Child 43: Head (Part) at 0x16B91B23B90
[SCAN]   Child 44: h���� (@c{��) at 0x16B9E48B2E0
[SCAN]   Child 45: Gyro (ObjectValue) at 0x16B8F9BD3E0
[SCAN]   Child 46: h��� (`�}��) at 0x16B741E4E30
[SCAN] Target 'HumanoidRootPart' not found among 46 children
[SCAN]   ✗ HumanoidRootPart not found anywhere
[SCAN]   ✓ Character name matches player name!
[SCAN]   Final confidence: 3/4 [HIGH CONFIDENCE]
[SCAN] 
[SCAN] === SCAN RESULTS (sorted by confidence) ===
[SCAN] 1. Offset 0x330 -> ShadowThijs (confidence: 3/4)
[SCAN]    Address: 0x16B653136E0
[SCAN]    Has Humanoid: true
[SCAN]    Has HumanoidRootPart: false
[SCAN] 
[SCAN] === RECOMMENDATION ===
[SCAN] RECOMMENDED: Use CHARACTER_OFFSET = 0x330
[SCAN] This offset points to: ShadowThijs with confidence 3/4
[SCAN] 
[SCAN] === POSITION TESTING ===
[SCAN] Testing with character at: 0x16B653136E0
[SCAN] Looking for HumanoidRootPart in character...
[SCAN] --- Searching for Humanoid in character ---
[SCAN] Searching for 'Humanoid' in parent 0x16B653136E0
[SCAN] Children list object at: 0x16B0C44DF38
[SCAN] Scanning children from 0x16BB52E7570 to 0x16BB52E76E0
[SCAN]   Child 1: Humanoid (Humanoid) at 0x16BB1150CB0
[SCAN] Found target 'Humanoid' at 0x16BB1150CB0
[SCAN] ✓ Humanoid found, now searching for HumanoidRootPart inside it...
[SCAN] --- Searching for HumanoidRootPart inside Humanoid ---
[SCAN] Searching for 'HumanoidRootPart' in parent 0x16BB1150CB0
[SCAN] Children list object at: 0x16B0C44DA38
[SCAN] Scanning children from 0x16B0C4537A0 to 0x16B0C4537D0
[SCAN]   Child 1: Animator (Animator) at 0x16B9E9CC720
[SCAN]   Child 2: �z��� (�����) at 0x16B9E498120
[SCAN]   Child 3:  �ak (�
4k) at 0x16BA8AB1BC0
[SCAN]   Child 4: 0��� (�|���) at 0x16B9E4986F0
[SCAN]   Child 5: Status (Status) at 0x16B653133E0
[SCAN]   Child 6: ����� (0����) at 0x16B9E489BA0
[SCAN] Target 'HumanoidRootPart' not found among 6 children
[SCAN] HumanoidRootPart not found inside Humanoid, trying fallback...
[SCAN] --- Fallback: Searching for HumanoidRootPart directly in character ---
[SCAN] Searching for 'HumanoidRootPart' in parent 0x16B653136E0
[SCAN] Children list object at: 0x16B0C44DF38
[SCAN] Scanning children from 0x16BB52E7570 to 0x16BB52E76E0
[SCAN]   Child 1: Humanoid (Humanoid) at 0x16BB1150CB0
[SCAN]   Child 2: Failed to read name (��w��) at 0x16B9E498810
[SCAN]   Child 3: PM�k (Folder) at 0x16BBA9EDCA0
[SCAN]   Child 4: Failed to read name (h���) at 0x16B9E498450
[SCAN]   Child 5: Weapon (Folder) at 0x16BBA9EE150
[SCAN]   Child 6: �z��� (h���) at 0x16B9E498840
[SCAN]   Child 7: Animate (LocalScript) at 0x16B5ACF9910
[SCAN]   Child 8: Failed to read name (�����) at 0x16B9E4988A0
[SCAN]   Child 9: Mounts (Folder) at 0x16BBA9EE240
[SCAN]   Child 10: h��� (h���) at 0x16B9E499800
[SCAN]   Child 11: Avatar (Folder) at 0x16BBA9EF140
[SCAN]   Child 12: Failed to read name (h���) at 0x16B9E499860
[SCAN]   Child 13: Health (Script) at 0x16B5ACF7820
[SCAN]   Child 14: ���� (H����) at 0x16B9E498F90
[SCAN]   Child 15: Body Colors (BodyColors) at 0x16B91AF6D10
[SCAN]   Child 16: Failed to read name (�����) at 0x16B9E499710
[SCAN]   Child 17: CharacterMesh (CharacterMesh) at 0x16BDE3FA460
[SCAN]   Child 18: �$��� (�n{��) at 0x16B9E499380
[SCAN]   Child 19: CharacterMesh (CharacterMesh) at 0x16BDE3FA570
[SCAN]   Child 20: ���� (�n{��) at 0x16B9E499620
[SCAN]   Child 21: CharacterMesh (CharacterMesh) at 0x16BDE3FCAA0
[SCAN]   Child 22: Failed to read name (�n{��) at 0x16B9E499260
[SCAN]   Child 23: CharacterMesh (CharacterMesh) at 0x16BDE3FC440
[SCAN]   Child 24: Failed to read name (�n{��) at 0x16B9E498E10
[SCAN]   Child 25: CharacterMesh (CharacterMesh) at 0x16BDE3FCCC0
[SCAN]   Child 26: ���� (�n{��) at 0x16B9E498E40
[SCAN]   Child 27: Hat1 (Accessory) at 0x16B5ACF7C00
[SCAN]   Child 28: @���� (�b}��) at 0x16B9E4995C0
[SCAN]   Child 29: VarietyShades02 (Accessory) at 0x16B5ACF85B0
[SCAN]   Child 30: ���� (�b}��) at 0x16B9E4995F0
[SCAN]   Child 31: Left Leg (Part) at 0x16B91B23FF0
[SCAN]   Child 32: Failed to read name (@c{��) at 0x16B9E499500
[SCAN]   Child 33: ��=k (Part) at 0x16B91B232D0
[SCAN]   Child 34: Failed to read name (@c{��) at 0x16B9E499C20
[SCAN]   Child 35: Right Leg (Part) at 0x16B91B22C40
[SCAN]   Child 36: Failed to read name (@c{��) at 0x16B9E489600
[SCAN]   Child 37: Right Arm (Part) at 0x16B91B230A0
[SCAN]   Child 38: ����� (@c{��) at 0x16B9E489C00
[SCAN]   Child 39: Left Arm (Part) at 0x16B91B22E70
[SCAN]   Child 40:  (@c{��) at 0x16B9E48A140
[SCAN]   Child 41: Torso (Part) at 0x16B91B23500
[SCAN]   Child 42: ���� (@c{��) at 0x16B9E48A8C0
[SCAN]   Child 43: Head (Part) at 0x16B91B23B90
[SCAN]   Child 44: h���� (@c{��) at 0x16B9E48B2E0
[SCAN]   Child 45: Gyro (ObjectValue) at 0x16B8F9BD3E0
[SCAN]   Child 46: h��� (`�}��) at 0x16B741E4E30
[SCAN] Target 'HumanoidRootPart' not found among 46 children
[SCAN] --- End HumanoidRootPart Search ---
[SCAN] ERROR: HumanoidRootPart not found anywhere in character
[SCAN] This character may not be a valid player character
[SCAN] 
[SCAN] === SCAN COMPLETE ===
[SCAN] Log saved to: character_offset_scan_1748886999.txt

=== END OF LOG ===