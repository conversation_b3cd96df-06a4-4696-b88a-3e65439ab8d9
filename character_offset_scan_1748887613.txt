=== ROBLOX CHARACTER OFFSET SCAN LOG ===
Generated by: Roblox DataModel Navigation Script
Engine time: 1748887613
File: character_offset_scan_1748887613.txt

=== SCAN DATA ===

[SCAN] === CHARACTER OFFSET SCANNING STARTED ===
[SCAN] Engine time: 1748887613
[SCAN] 
[SCAN] Scanning player: <PERSON><PERSON>hij<PERSON> at 0x20585EB27A0
[SCAN] Players service address: 0x205861F6C30
[SCAN] Current CHARACTER_OFFSET: 0x1D0
[SCAN] 
[SCAN] Testing current CHARACTER_OFFSET (0x1D0):
[SCAN]   Current offset points to: 0xF
[SCAN]   Invalid address range - too small or too large
[SCAN] 
[SCAN] Scanning for character pointer in player memory (0x100 to 0x400)...
[SCAN] Found Model at offset 0x330: ShadowThijs
[SCAN]   Checking for Humanoid and HumanoidRootPart...
[SCAN]   --- Searching for Humanoid ---
[SCAN] Searching for 'Humanoid' in parent 0x205231816B0
[SCAN] Children list object at: 0x20588B50478
[SCAN] Scanning children from 0x205890B45F0 to 0x205890B4760
[SCAN]   Child 1: Humanoid (Humanoid) at 0x2054EFAEC90
[SCAN] Found target 'Humanoid' at 0x2054EFAEC90
[SCAN]   ✓ Humanoid found at: 0x2054EFAEC90
[SCAN]   --- Testing direct HumanoidRootPart offset (0x210) ---
[SCAN]   Direct offset points to: No Name Ptr at 0x3F800000
[SCAN]   ✗ Direct offset doesn't point to HumanoidRootPart
[SCAN]   --- Fallback: Searching for HumanoidRootPart in character children ---
[SCAN] Searching for 'HumanoidRootPart' in parent 0x205231816B0
[SCAN] Children list object at: 0x20588B50478
[SCAN] Scanning children from 0x205890B45F0 to 0x205890B4760
[SCAN]   Child 1: Humanoid (Humanoid) at 0x2054EFAEC90
[SCAN]   Child 2: �_��� (��w��) at 0x20585EDD0F0
[SCAN]   Child 3: �*)� (Folder) at 0x205200941C0
[SCAN]   Child 4: ����� (h���) at 0x20585EDCF70
[SCAN]   Child 5: Weapon (Folder) at 0x20520092FF0
[SCAN]   Child 6: 0��� (h���) at 0x20585EDCE50
[SCAN]   Child 7: Animate (LocalScript) at 0x20580EAAE40
[SCAN]   Child 8: �_��� (�����) at 0x20585EDCE80
[SCAN]   Child 9: Mounts (Folder) at 0x20520092780
[SCAN]   Child 10: Failed to read name (h���) at 0x2059C81B400
[SCAN]   Child 11: Avatar (Folder) at 0x20520093E00
[SCAN]   Child 12: Failed to read name (h���) at 0x2059C81AF20
[SCAN]   Child 13: Health (Script) at 0x20580EA7040
[SCAN]   Child 14: Failed to read name (H����) at 0x2059C81B880
[SCAN]   Child 15: Body Colors (BodyColors) at 0x20509469370
[SCAN]   Child 16: Failed to read name (�����) at 0x2059C81B4F0
[SCAN]   Child 17: CharacterMesh (CharacterMesh) at 0x205322AB870
[SCAN]   Child 18: @���� (�n{��) at 0x2059C81AFE0
[SCAN]   Child 19: CharacterMesh (CharacterMesh) at 0x205322AA330
[SCAN]   Child 20: hN��� (�n{��) at 0x2059C81B8B0
[SCAN]   Child 21: CharacterMesh (CharacterMesh) at 0x205322AA660
[SCAN]   Child 22: Failed to read name (�n{��) at 0x2059C81ADD0
[SCAN]   Child 23: CharacterMesh (CharacterMesh) at 0x205322AB980
[SCAN]   Child 24: �=�e��.P��� (�n{��) at 0x2059C81B010
[SCAN]   Child 25: CharacterMesh (CharacterMesh) at 0x205322ABA90
[SCAN]   Child 26:  <��� (�n{��) at 0x2059C81B040
[SCAN]   Child 27: Hat1 (Accessory) at 0x20580EADEB0
[SCAN]   Child 28: Failed to read name (�b}��) at 0x2059C81B100
[SCAN]   Child 29: VarietyShades02 (Accessory) at 0x20580EABBD0
[SCAN]   Child 30: @C��� (�b}��) at 0x2059C81B220
[SCAN]   Child 31: �;��� (Part) at 0x2059957E530
[SCAN]   Child 32: Failed to read name (@c{��) at 0x2059C81B310
[SCAN]   Child 33: Right Arm (Part) at 0x2059957EBC0
[SCAN]   Child 34: Failed to read name (@c{��) at 0x2059C81C3F0
[SCAN]   Child 35: Right Leg (Part) at 0x2059957F480
[SCAN]   Child 36: Failed to read name (@c{��) at 0x2059C820F80
[SCAN]   Child 37: Left Arm (Part) at 0x20599580600
[SCAN]   Child 38: @�ћ (@c{��) at 0x2059C820EF0
[SCAN]   Child 39: Torso (Part) at 0x2059957D810
[SCAN]   Child 40:  (@c{��) at 0x2057EC8AF60
[SCAN]   Child 41: Left Leg (Part) at 0x2059957DA40
[SCAN]   Child 42: Failed to read name (@c{��) at 0x2051FD6ECE0
[SCAN]   Child 43: Head (Part) at 0x2059957DC70
[SCAN]   Child 44: Failed to read name (@c{��) at 0x205997C9B40
[SCAN]   Child 45: Gyro (ObjectValue) at 0x205245143F0
[SCAN]   Child 46: Failed to read name (`�}��) at 0x205A4EDD560
[SCAN] Target 'HumanoidRootPart' not found among 46 children
[SCAN]   ✗ HumanoidRootPart not found in character children
[SCAN]   ✓ Character name matches player name!
[SCAN]   Final confidence: 3/4 [HIGH CONFIDENCE]
[SCAN] 
[SCAN] === SCAN RESULTS (sorted by confidence) ===
[SCAN] 1. Offset 0x330 -> ShadowThijs (confidence: 3/4)
[SCAN]    Address: 0x205231816B0
[SCAN]    Has Humanoid: true
[SCAN]    Has HumanoidRootPart: false
[SCAN] 
[SCAN] === RECOMMENDATION ===
[SCAN] RECOMMENDED: Use CHARACTER_OFFSET = 0x330
[SCAN] This offset points to: ShadowThijs with confidence 3/4
[SCAN] 
[SCAN] === POSITION TESTING ===
[SCAN] Testing with character at: 0x205231816B0
[SCAN] Looking for HumanoidRootPart in character...
[SCAN] --- Method 1: Testing direct HumanoidRootPart offset ---
[SCAN] Direct offset (0x210) points to: No Name Ptr at 0x3F800000
[SCAN] ✗ Direct offset doesn't point to HumanoidRootPart
[SCAN] --- Method 2: Searching for HumanoidRootPart in character children ---
[SCAN] Searching for 'HumanoidRootPart' in parent 0x205231816B0
[SCAN] Children list object at: 0x20588B50478
[SCAN] Scanning children from 0x205890B45F0 to 0x205890B4760
[SCAN]   Child 1: Humanoid (Humanoid) at 0x2054EFAEC90
[SCAN]   Child 2: �_��� (��w��) at 0x20585EDD0F0
[SCAN]   Child 3: �*)� (Folder) at 0x205200941C0
[SCAN]   Child 4: ����� (h���) at 0x20585EDCF70
[SCAN]   Child 5: Weapon (Folder) at 0x20520092FF0
[SCAN]   Child 6: 0��� (h���) at 0x20585EDCE50
[SCAN]   Child 7: Animate (LocalScript) at 0x20580EAAE40
[SCAN]   Child 8: �_��� (�����) at 0x20585EDCE80
[SCAN]   Child 9: Mounts (Folder) at 0x20520092780
[SCAN]   Child 10: Failed to read name (h���) at 0x2059C81B400
[SCAN]   Child 11: Avatar (Folder) at 0x20520093E00
[SCAN]   Child 12: Failed to read name (h���) at 0x2059C81AF20
[SCAN]   Child 13: Health (Script) at 0x20580EA7040
[SCAN]   Child 14: Failed to read name (H����) at 0x2059C81B880
[SCAN]   Child 15: Body Colors (BodyColors) at 0x20509469370
[SCAN]   Child 16: Failed to read name (�����) at 0x2059C81B4F0
[SCAN]   Child 17: CharacterMesh (CharacterMesh) at 0x205322AB870
[SCAN]   Child 18: @���� (�n{��) at 0x2059C81AFE0
[SCAN]   Child 19: CharacterMesh (CharacterMesh) at 0x205322AA330
[SCAN]   Child 20: hN��� (�n{��) at 0x2059C81B8B0
[SCAN]   Child 21: CharacterMesh (CharacterMesh) at 0x205322AA660
[SCAN]   Child 22: Failed to read name (�n{��) at 0x2059C81ADD0
[SCAN]   Child 23: CharacterMesh (CharacterMesh) at 0x205322AB980
[SCAN]   Child 24: �=�e��.P��� (�n{��) at 0x2059C81B010
[SCAN]   Child 25: CharacterMesh (CharacterMesh) at 0x205322ABA90
[SCAN]   Child 26:  <��� (�n{��) at 0x2059C81B040
[SCAN]   Child 27: Hat1 (Accessory) at 0x20580EADEB0
[SCAN]   Child 28: Failed to read name (�b}��) at 0x2059C81B100
[SCAN]   Child 29: VarietyShades02 (Accessory) at 0x20580EABBD0
[SCAN]   Child 30: @C��� (�b}��) at 0x2059C81B220
[SCAN]   Child 31: �;��� (Part) at 0x2059957E530
[SCAN]   Child 32: Failed to read name (@c{��) at 0x2059C81B310
[SCAN]   Child 33: Right Arm (Part) at 0x2059957EBC0
[SCAN]   Child 34: Failed to read name (@c{��) at 0x2059C81C3F0
[SCAN]   Child 35: Right Leg (Part) at 0x2059957F480
[SCAN]   Child 36: Failed to read name (@c{��) at 0x2059C820F80
[SCAN]   Child 37: Left Arm (Part) at 0x20599580600
[SCAN]   Child 38: @�ћ (@c{��) at 0x2059C820EF0
[SCAN]   Child 39: Torso (Part) at 0x2059957D810
[SCAN]   Child 40:  (@c{��) at 0x2057EC8AF60
[SCAN]   Child 41: Left Leg (Part) at 0x2059957DA40
[SCAN]   Child 42: Failed to read name (@c{��) at 0x2051FD6ECE0
[SCAN]   Child 43: Head (Part) at 0x2059957DC70
[SCAN]   Child 44: Failed to read name (@c{��) at 0x205997C9B40
[SCAN]   Child 45: Gyro (ObjectValue) at 0x205245143F0
[SCAN]   Child 46: Failed to read name (`�}��) at 0x205A4EDD560
[SCAN] Target 'HumanoidRootPart' not found among 46 children
[SCAN] ✗ HumanoidRootPart not found in character children
[SCAN] --- Method 3: Searching inside Humanoid ---
[SCAN] Searching for 'Humanoid' in parent 0x205231816B0
[SCAN] Children list object at: 0x20588B50478
[SCAN] Scanning children from 0x205890B45F0 to 0x205890B4760
[SCAN]   Child 1: Humanoid (Humanoid) at 0x2054EFAEC90
[SCAN] Found target 'Humanoid' at 0x2054EFAEC90
[SCAN] ✓ Humanoid found, searching for HumanoidRootPart inside it...
[SCAN] Searching for 'HumanoidRootPart' in parent 0x2054EFAEC90
[SCAN] Children list object at: 0x20588B50778
[SCAN] Scanning children from 0x20588B51A20 to 0x20588B51A50
[SCAN]   Child 1: Animator (Animator) at 0x2051EEFDAA0
[SCAN]   Child 2: ���� (�����) at 0x20585EDD960
[SCAN]   Child 3: P�i{ ( ��M) at 0x2059BCCC690
[SCAN]   Child 4: ����� (�|���) at 0x20585EDD2D0
[SCAN]   Child 5: Status (Status) at 0x20521A82D00
[SCAN]   Child 6: ����� (0����) at 0x205997C9AE0
[SCAN] Target 'HumanoidRootPart' not found among 6 children
[SCAN] ✗ HumanoidRootPart not found inside Humanoid
[SCAN] --- End HumanoidRootPart Search ---
[SCAN] ERROR: HumanoidRootPart not found using any method
[SCAN] This character may not be a valid player character
[SCAN] 
[SCAN] === SCAN COMPLETE ===
[SCAN] Log saved to: character_offset_scan_1748887613.txt

=== END OF LOG ===