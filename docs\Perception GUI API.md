
📁 Tab, Subtab, and Panel Creation

    gui.get_tab(name) – Retrieves a predefined tab ("aimbot", "visuals", "lua", "settings").
    tab:create_panel(label, small_panel?) – Creates a panel inside a tab.
    tab:create_subtab(label) – Creates a subtab inside a tab.
    subtab:create_panel(label, small_panel?) – Creates a panel inside a subtab.



🧱 Panel Widgets

    panel:add_checkbox(label) – Adds a checkbox.
    panel:add_slider_int(label, min, max, default) – Adds an integer slider.
    panel:add_slider_float(label, min, max, default) – Adds a float slider.
    panel:add_button(label, function) – Adds a button with a Lua callback.
    panel:add_text(label) – Adds a static text label.
    panel:add_input_text(label, default) – Adds a text input box.
    panel:add_color_picker(label, r, g, b, a) – Adds a color picker.
    panel:add_keybind(label, key, mode) – Adds a keybind.
    panel:add_single_select(label, list, default_index?) – Adds a single-select dropdown.
    panel:add_multi_select(label, list) – Adds a multi-select list.
    panel:add_singleselect_list(label, list) – Adds a single-select list.
    panel:add_multiselect_list(label, list) – Adds a multi-select list.



🎛️ Element Control

    element:set_active(bool) – Shows or hides any GUI element.



✅ Checkbox

    checkbox:get() – Gets the checkbox state.
    checkbox:set(bool) – Sets the checkbox state.



🎚️ Slider Int / Float

    slider_int:get() / slider_float:get() – Gets slider value.
    slider_int:set(value) / slider_float:set(value) – Sets slider value.



📝 Text and Input

    text:get() – Gets the text label.
    text:set(str) – Sets the text label.
    input_text:get() – Gets the current input.
    input_text:set(str) – Sets the input text.



🎨 Color Picker

    color_picker:get() – Gets RGBA values.
    color_picker:set(r, g, b, a) – Sets RGBA color.



⌨️ Keybind

    keybind:get_key() – Gets bound key code.
    keybind:set_key(k) – Sets key code.
    keybind:get_mode() – Gets keybind mode.
    keybind:set_mode(mode) – Sets keybind mode.
    keybind:is_active() – Checks if keybind is active.



🔽 Single / Multi Select

    single_select:get() – Gets selected index.
    single_select:set(index) – Sets selected index.
    multi_select:get(index) – Gets active state.
    multi_select:set(index, bool) – Sets active state.



📋 Lists

    input_list_single:get() – Gets selected index.
    input_list_single:set(index) – Sets selected index.
    input_list_multi:get(index) – Gets active state of an item.
    input_list_multi:set(index, bool) – Sets active state.



🎯 Keybind Modes

    key_mode.onhotkey, key_mode.offhotkey, key_mode.toggle,
    key_mode.singlepress, key_mode.always_on – Keybind mode constants.

Example:
LUA:
```lua
ui_state = {}
local tab = gui.get_tab("lua")
ui_state.panel = tab:create_panel("Full API Test", true)
-- Core widgets
ui_state.checkbox = ui_state.panel:add_checkbox("Enable Feature")
ui_state.slider_int = ui_state.panel:add_slider_int("Int Slider", 0, 10, 5)
ui_state.slider_float = ui_state.panel:add_slider_float("Float Slider", 0.0, 1.0, 0.5)
ui_state.input = ui_state.panel:add_input_text("Input Field", "default")
ui_state.text = ui_state.panel:add_text("Dynamic Text")
ui_state.keybind = ui_state.panel:add_keybind("Keybind", 45, key_mode.toggle)
ui_state.color_picker = ui_state.panel:add_color_picker("Color Picker", 255, 0, 0, 255)
-- Single & multi select
ui_state.single_select = ui_state.panel:add_single_select("Single Select", { "Option 1", "Option 2", "Option 3" }, 2)
ui_state.multi_select = ui_state.panel:add_multi_select("Multi Select", { "A", "B", "C" })
ui_state.multi_select:set(1, true)
ui_state.multi_select:set(3, true)
-- Input lists
ui_state.input_list_single = ui_state.panel:add_singleselect_list("Input List Single", { "Red", "Green", "Blue" })
ui_state.input_list_single:set(2)
ui_state.input_list_multi = ui_state.panel:add_multiselect_list("Input List Multi", { "One", "Two", "Three" })
ui_state.input_list_multi:set(1, true)
ui_state.input_list_multi:set(2, true)
-- Button with full test logic
ui_state.button = ui_state.panel:add_button("Test Button", function()
    print("[lua] Button pressed!")
    print("[lua] checkbox:", ui_state.checkbox:get())
    print("[lua] int slider:", ui_state.slider_int:get())
    print("[lua] float slider:", ui_state.slider_float:get())
    print("[lua] input:", ui_state.input:get())
    print("[lua] single select:", ui_state.single_select:get())
    print("[lua] color:", table.unpack({ ui_state.color_picker:get() }))
    print("[lua] keybind key:", ui_state.keybind:get_key(), "mode:", ui_state.keybind:get_mode(), "active:", ui_state.keybind:is_active())
    print("[lua] multi_select[1]:", ui_state.multi_select:get(1))
    print("[lua] multi_select[3]:", ui_state.multi_select:get(3))
    print("[lua] input_list_single:", ui_state.input_list_single:get())
    print("[lua] input_list_multi[1]:", ui_state.input_list_multi:get(1))
    print("[lua] input_list_multi[2]:", ui_state.input_list_multi:get(2))
    -- Toggle visibility
    local toggle = not ui_state.checkbox:get()
    for k, v in pairs(ui_state) do
        if v.set_active then v:set_active(toggle) end
    end
    print("[lua] Toggled all element visibility to:", toggle)
end)

-- Initial setup for values
ui_state.checkbox:set(true)
ui_state.slider_int:set(7)
ui_state.slider_float:set(0.75)
ui_state.input:set("new input text")
ui_state.single_select:set(1)
ui_state.color_picker:set(0, 255, 128, 200)
ui_state.keybind:set_key(87)
ui_state.keybind:set_mode(key_mode.onhotkey)
ui_state.text:set("Click the button to see values!")
```
