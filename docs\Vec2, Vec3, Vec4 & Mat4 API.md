
vec2 – 2D Vector API

📌 Constructor

    vec2(x, y) – Constructs a 2D vector.

➕ Operators

    vec2 + vec2 – Adds two vectors.
    vec2 - vec2 – Subtracts two vectors.
    vec2 * scalar – Multiplies vector by scalar.
    vec2 / scalar – Divides vector by scalar.
    -vec2 – Returns the negated vector.
    #vec2 – Returns the length of the vector.
    vec2 == vec2 – Compares two vectors for equality.

🧩 Fields

    vec2.x, vec2.y – Access vector components.

🛠️ Methods

    vec2:length() – Returns the vector's length.
    vec2:length_squared() – Returns the squared length.
    vec2:normalize() – Returns a normalized copy.
    vec2:dot(v) – Returns the dot product with another vector.
    vec2:distance(v) – Returns distance to another vector.
    vec2:clone() – Returns a new copy of the vector.
    vec2:perpendicular() – Returns a perpendicular vector.
    vec2:angle() – Returns the angle in radians from the X-axis.
    vec2:rotate(radians) – Rotates the vector by angle in radians.
    vec2:lerp(v, t) – Linearly interpolates toward vector v by factor t.
    vec2:project_onto(v) – Projects this vector onto vector v.
    vec2:clamp_length(min, max) – Clamps the vector's length between min and max.
    vec2:reflect(normal) – Reflects the vector off the given normal.

💾 Memory Access

    vec2.read_float(address) – Reads a vec2 from memory (float precision).
    vec2.read_double(address) – Reads a vec2 from memory (double precision).
    vec2.write_float(address, v) – Writes a vec2 to memory (float precision).
    vec2.write_double(address, v) – Writes a vec2 to memory (double precision).



🧭 vec3 – 3D Vector API

📌 Constructor

    vec3(x, y, z) – Constructs a 3D vector.

➕ Operators

    vec3 + vec3 – Adds two vectors.
    vec3 - vec3 – Subtracts two vectors.
    vec3 * scalar – Multiplies the vector by a scalar.
    vec3 / scalar – Divides the vector by a scalar.
    -vec3 – Negates the vector.
    #vec3 – Returns the length of the vector.
    vec3 == vec3 – Compares two vectors for equality.

🧩 Fields

    vec3.x, vec3.y, vec3.z – Access individual components.

🛠️ Methods

📏 Length & Normalization

    vec3:length() – Returns the 3D vector's length.
    vec3:length_2d() – Returns the 2D (XY) length.
    vec3:length_2d_squared() – Returns the squared 2D length.
    vec3:normalize() – Returns a normalized version of the vector.
    vec3:clamp_length(min, max) – Clamps vector length between min and max.

⚙️ Math & Direction

    vec3:dot(v) – Dot product with another vector.
    vec3:cross(v) – Cross product with another vector.
    vec3:distance(v) – Distance to another vector.
    vec3:angle_between(v) – Angle in radians between vectors.
    vec3:reflect(normal) – Reflects the vector off a surface normal.
    vec3.project_onto(v) – Projects this vector onto vector v.
    vec3:rotate_axis(angle, axis) – Rotates the vector around an axis.

📦 Utility

    vec3:clone() – Returns a copy of the vector.
    vec3:lerp(v, t) – Linear interpolation with vector v.
    vec3:slerp(v, t) – Spherical linear interpolation.

📐 Angle & Orientation

    vec3:to_forward() – Converts angles to a forward direction vector.
    vec3:to_right() – Converts angles to a right vector.
    vec3:to_up() – Converts angles to an up vector.
    vec3:to_qangle() – Returns pitch, yaw, roll as a vector.
    vec3:normalize_angles() – Normalizes pitch/yaw.
    vec3:clamp_angles() – Clamps pitch/yaw; sets roll to 0.
    vec3.normalize_angle(angle) – Normalizes a single angle value.
    vec3.from_qangle(pitch, yaw) – Converts pitch/yaw into a direction vector.

💾 Memory Access

    vec3.read_float(address) – Reads a vec3 from memory (float).
    vec3.read_double(address) – Reads a vec3 from memory (double).
    vec3.write_float(address, v) – Writes a vec3 to memory (float).
    vec3.write_double(address, v) – Writes a vec3 to memory (double).



🧊 vec4 – 4D Vector API

📌 Constructor

    vec4(x, y, z, w) – Constructs a 4D vector.

➕ Operators

    vec4 + vec4 – Adds two vectors.
    vec4 - vec4 – Subtracts two vectors.
    vec4 * scalar – Multiplies the vector by a scalar.
    vec4 / scalar – Divides the vector by a scalar.
    -vec4 – Negates the vector.
    #vec4 – Returns the vector's length.
    vec4 == vec4 – Compares two vectors for equality.

🧩 Fields

    vec4.x, vec4.y, vec4.z, vec4.w – Access individual components.


🛠️ Methods

📏 Length & Normalization

    vec4:length() – Returns the vector's magnitude.
    vec4:normalize() – Returns a normalized version.

⚙️ Math & Utility

    vec4:dot(v) – Computes the dot product with another vec4.
    vec4:clone() – Returns a copy of the vector.
    vec4:lerp(v, t) – Linearly interpolates toward v by factor t.
    vec4:clamp_length(min, max) – Clamps the vector length.
    vec4.project_onto(v) – Projects this vector onto another.
    vec4:reflect(normal) – Reflects the vector off a surface normal.

💾 Memory Access

    vec4.read_float(address) – Reads a vec4 from memory (float precision).
    vec4.read_double(address) – Reads a vec4 from memory (double precision).
    vec4.write_float(address, v) – Writes a vec4 to memory (float precision).
    vec4.write_double(address, v) – Writes a vec4 to memory (double precision).



🧱 mat4 – 4×4 Matrix API

📌 Constructor

    mat4() – Creates a 4×4 identity matrix.

➕ Operators

    mat4 * mat4 – Multiplies two matrices.
    mat4 * vec4 – Transforms a vec4 using the matrix.


🛠️ Methods

📏 Access & Structure

    mat4:get(row, col) – Gets the value at row, col (1–4 based).
    mat4:set(row, col, value) – Sets the value at row, col.
    mat4:row(index) – Returns a row as vec4.
    mat4:column(index) – Returns a column as vec4.
    mat4:clone() – Returns a copy of the matrix.
    mat4:to_table() – Converts matrix to a nested Lua table.
    mat4.from_table(table) – Constructs a matrix from a Lua table.

🔄 Transformations

    mat4:transpose() – Returns the transposed matrix.
    mat4:inverse() – Returns the inverse matrix.
    mat4:determinant() – Returns the determinant.
    mat4:scale(vec3) – Scales the matrix by a vec3.
    mat4:translate(vec3) – Applies translation using a vec3.
    mat4:rotate(angle, axis) – Rotates the matrix around an axis by angle (radians).
    mat4:apply_to_vec3(vec3) – Applies the matrix transformation to a vec3.

📐 Decomposition & Comparison

    mat4:decompose() – Returns position, rotation, and scale components.
    mat4:equals(other, tolerance?) – Compares with another matrix (epsilon optional).
    mat4:is_identity() – Returns true if the matrix is identity.

📦 Memory Access

    mat4.read(address) – Reads a mat4 from memory.

🖼️ Special Matrix Constructors

    mat4.perspective(fov_y, aspect, near, far) – Creates a perspective projection matrix.
    mat4.orthographic(left, right, bottom, top, near, far) – Creates an orthographic projection matrix.
    mat4.trs(position, rotation_quat, scale) – Creates a matrix from translation, rotation (quat), and scale.
    mat4.look_at(eye, target, up) – Creates a view matrix pointing at target from eye.

