
🧮 math – Utility Math API

🧷 Core Functions

    math.clamp(x, min, max) – Clamps a value between a minimum and maximum.
    math.lerp(a, b, t) – Linearly interpolates between a and b by t.
    math.round(x) – Rounds x to the nearest integer.
    math.round_up(x) – Rounds x upward (ceiling).
    math.round_down(x) – Rounds x downward (floor).
    math.round_to_nearest(x, step) – Rounds x to the nearest multiple of step.
    math.sign(x) – Returns the sign of x as -1, 0, or 1.
    math.map(x, in_min, in_max, out_min, out_max) – Maps x from one range to another.
    math.saturate(x) – Clamps x between 0 and 1.


🧪 Validation & Checks

    math.is_nan(x) – Returns true if x is NaN.
    math.is_inf(x) – Returns true if x is infinite.


📈 Interpolation & Range Tools

    math.smoothstep(edge0, edge1, x) – Smooth Hermite interpolation between edge0 and edge1.
    math.inverse_lerp(a, b, x) – Computes normalized parameter t where x lies between a and b.
    math.fract(x) – Returns the fractional part of x.
    math.wrap(x, min, max) – Wraps x within the range min to max.

