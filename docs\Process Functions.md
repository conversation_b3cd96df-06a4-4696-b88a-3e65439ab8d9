# Process Functions

# proc.attach_by_pid

Signature: proc.attach_by_pid(process_id, has_corrupt_cr3)

Description: Attach to a process using its PID.

Parameters:

    process_id (integer): The PID of the process to attach to

    has_corrupt_cr3 (boolean, optional): Set to true for games protected by Easy Anti-Cheat to resolve corrupted CR3

Returns:

    boolean: true if attached successfully, false otherwise

Example:

```lua
local process_id = 1234  -- Replace with actual PID
if proc.attach_by_pid(process_id) then
    engine.log("Successfully attached to process " .. process_id, 0, 255, 0, 255)
else
    engine.log("Failed to attach to process " .. process_id, 255, 0, 0, 255)
end
```

# proc.attach_by_name

Signature: proc.attach_by_name(process_name, has_corrupt_cr3)

Description: Attach to a process using its name.

Parameters:

    process_name (string): The name of the process to attach to (e.g., "notepad.exe")

    has_corrupt_cr3 (boolean, optional): Set to true for games protected by Easy Anti-Cheat to resolve corrupted CR3

Returns:

    boolean: true if attached successfully, false otherwise

Example:

```lua
if not proc.attach_by_name("notepad.exe") then
    engine.log("Failed to attach to Notepad!", 255, 0, 0, 255)
    return
end
engine.log("Successfully attached to Notepad!", 0, 255, 0, 255)
```

# proc.attach_by_window

Signature: proc.attach_by_window(window_class, window_name, has_corrupt_cr3)

Description: Attach to a process using its window class and window name.

Parameters:

    window_class (string): The window class

    window_name (string): The window name

    has_corrupt_cr3 (boolean, optional): Set to true for games protected by Easy Anti-Cheat to resolve corrupted CR3

Returns:

    boolean: true if attached successfully, false otherwise

Example:

```lua
if not proc.attach_by_window("Notepad", "Untitled - Notepad") then
    engine.log("Failed to attach to Notepad window!", 255, 0, 0, 255)
    return
end
engine.log("Successfully attached to Notepad window!", 0, 255, 0, 255)
```

# proc.is_attached

Signature: proc.is_attached()

Description: Check if a process is currently attached.

Parameters: None

Returns:

    boolean: true if a process is attached, false otherwise

Example:

```lua
if proc.is_attached() then
    engine.log("Process is attached", 0, 255, 0, 255)
else
    engine.log("No process is attached", 255, 0, 0, 255)
end
```

# Complete Example

```lua
-- Attempt to attach to Notepad by process name
if not proc.attach_by_name("notepad.exe") then
    engine.log("Failed to attach to Notepad!", 255, 0, 0, 255)
    return
end
engine.log("Successfully attached to Notepad!", 0, 255, 0, 255)

-- Get the base address of Notepad
local base_address = proc.base_address()
if base_address == nil then
    engine.log("Failed to get Notepad base address!", 255, 0, 0, 255)
    return
end
engine.log("Base Address: " .. string.format("0x%X", base_address), 255, 255, 255, 255)

-- Define the offset for e_lfanew in the DOS header
local e_lfanew_offset = 0x3C
-- Read the e_lfanew value (DWORD) from the PE header
local e_lfanew = proc.read_int32(base_address + e_lfanew_offset)
if e_lfanew == nil then
    engine.log("Failed to read e_lfanew!", 255, 0, 0, 255)
    return
end
engine.log("e_lfanew: " .. e_lfanew, 0, 255, 0, 255)
```

# Notes

    These functions do not create OpenProcess handles, they only set up process interaction.

    If an invalid argument is provided, an error is thrown.

    If the process cannot be found or attached, the function returns false.