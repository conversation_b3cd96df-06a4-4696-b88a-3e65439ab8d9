function getAllPlayerNames(PLAYER_SERVICE_ADDRESS)
    local player_names = {}

    if PLAYER_SERVICE_ADDRESS == 0 then
        print("Error: PLAYER_SERVICE_ADDRESS is null.")
        return player_names
    end

    -- 1. Get the address of the children management object from the Players service.
    local child_list_object_address = memory.read_uint32(PLAYER_SERVICE_ADDRESS + OFFSET_INSTANCE_TO_CHILDLIST_OBJ)
    if child_list_object_address == 0 then
        print("Error: Could not read child list object address from Players service.")
        return player_names
    end

    -- 2. Get the pointers to the beginning and end of the child entries array.
    local current_child_entry_address = memory.read_uint32(child_list_object_address + OFFSET_CHILDLIST_OBJ_TO_CHILD_ARRAY_BEGIN)
    local end_of_child_entries_address = memory.read_uint32(child_list_object_address + OFFSET_CHILDLIST_OBJ_TO_CHILD_ARRAY_END)

    if current_child_entry_address == 0 or end_of_child_entries_address == 0 then
        print("Error: Child array begin or end pointer is null. (Or no players)")
        return player_names
    end

    if current_child_entry_address == end_of_child_entries_address then
        print("No players found (child array is empty).")
        return player_names
    end
    
    -- 3. Iterate through the array of child entries.
    --    The loop continues as long as current_child_entry_address is less than end_of_child_entries_address.
    while current_child_entry_address < end_of_child_entries_address do
        -- Each entry in this array is CHILD_ARRAY_ENTRY_SIZE (e.g., 8 bytes).
        -- The actual pointer to the child Instance is at the start of this entry.
        local player_instance_address = memory.read_uint32(current_child_entry_address)

        if player_instance_address ~= 0 then
            -- We have a Player instance. Now, get its name.
            -- The name is a string object located at (player_instance_address + OFFSET_INSTANCE_TO_NAME_OBJ).
            local name_string_object_address = player_instance_address + OFFSET_INSTANCE_TO_NAME_OBJ
            
            -- Use your API's robust string reading function.
            local player_name = memory.read_roblox_string_object(name_string_object_address)
            
            if player_name and #player_name > 0 then
                table.insert(player_names, player_name)
            else
                -- print("Warning: Could not read name for player instance at 0x" .. string.format("%X", player_instance_address))
            end
        else
            -- print("Warning: Found a null player instance pointer in the list.")
        end

        -- Move to the next entry in the array.
        current_child_entry_address = current_child_entry_address + CHILD_ARRAY_ENTRY_SIZE
    end

    return player_names
end