
🎨 Using the Rendering API

- render.create_font("verdana.ttf", 25)
Creates a font with the specified file path and size. If only name is provided instead of filepath, it will try to create font from c:/windows/fonts/

- render.create_font_from_buffer("Verdana", 25, buffer_handle)
Creates a font with the specified font label, size, and buffer create via m.alloc.

- render.get_viewport_size()
Retrieves the screen size for reference.

- render.draw_line(x1, y1, x2, y2, r, g, b, a, thickness)
Draws a diagonal line.

- render.draw_rectangle(x, y, width, height, r, g, b, a, thickness, filled, rounding)
Draws a rectangle.

- render.draw_circle(x, y, radius, r, g, b, a, thickness, filled)
Draws a filled circle.

- render.draw_text(font, text, x, y, r, g, b, a, outline_thickness, o_r, o_g, o_b, o_a)
Draws text

- render.draw_triangle(x1, y1, x2, y2, x3, y3, r, g, b, a, thickness, filled)
Draws triangle.

- render.get_fps()
Gets overlay FPS

- render.create_bitmap_from_url(url)
Creates a bitmap from a URL.

- render.create_bitmap_from_buffer(buffer_handle)
Creates a bitmap from buffer handle

- render.create_bitmap_from_file(file_name)
Creates a bitmap from a file

- render.clip_start(x, y, width, height)
Begins a clipping region. All subsequent rendering will be restricted to this rectangle until render.clip_end() is called. Useful for scrollable UI panels or constrained draw areas.

- render.clip_end()
Ends the active clipping region started by render.clip_start. Restores full rendering output to the entire screen or parent context.

- render.draw_four_corner_gradient(x, y, width, height, r1, g1, b1, r2, g2, b2, r3, g3, b3, r4, g4, b4)
Draws a rectangle filled with a four-corner gradient. Each corner has its own RGB color value (top-left, top-right, bottom-left, bottom-right), blended smoothly across the area.

- render.draw_polygon(points_table, r, g, b, a, thickness, filled)
Draw polygons with an array of {x, y} points (minimum 3).

- render.draw_ellipse(x, y, rx, ry, r, g, b, a, thickness, filled)
Draw ellipses with full control over center, radii, color, thickness, and fill.

- render.draw_arc(x, y, rx, ry, start_angle, sweep_angle, r, g, b, a, thickness, filled)
Draw arcs or pie-slices with angle and color options.

- render.draw_gradient_line(x1, y1, x2, y2, color_table, thickness)
Draws a line with a gradient from color1 to color2.

- render.draw_gradient_rectangle(x, y, width, height, color_table, rounding)
Draws a gradient filled rectangle



This example demonstrates how to use the rendering functions in Lua.

LUA:
```lua
-- Create a font for rendering text
local font_handle = render.create_font("verdana.ttf", 25)

-- Get the screen size
local screen_size_x, screen_size_y = render.get_viewport_size()

-- Callback function for rendering
function enginetick_callback()
    -- Draw a white diagonal line
    render.draw_line(500, 500, 600, 600, 255, 255, 255, 255, 5)

    -- Draw a filled yellow rectangle
    render.draw_rectangle(700, 700, 50, 50, 255, 255, 0, 255, 1, true)

    -- Draw a filled red circle
    render.draw_circle(900, 900, 25, 255, 0, 0, 255, 5, true)

    -- Draw text in magenta with a white outline
    render.draw_text(font_handle, "Test", 1000, 1000, 255, 0, 255, 255, 5, 255, 255, 255, 255)

    -- Draw a red triangle
    render.draw_triangle(500, 500,600, 600, 800, 800, 255, 0, 0, 255, 1, false)
end
```

This example ensures smooth usage of the rendering API for drawing shapes, lines, and text in real-time. 🚀
