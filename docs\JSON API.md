
json – JSON Handling API

🛠️ Functions

    json.parse(data) – Parses a JSON-encoded string data and returns a Lua table.
    json.stringify(lua_table) – Converts a Lua table into a JSON-formatted string (with indentation).


LUA:
```lua
-- Example JSON string
local raw = '{"name": "Nightlings", "version": 1.0, "features": ["gui", "overlay", "lua"]}'

-- Parse JSON into Lua table
local tbl = json.parse(raw)
engine.log("Project: " .. tbl.name)
engine.log("Version: " .. tostring(tbl.version))
engine.log("First feature: " .. tbl.features[1])

-- Modify the table
tbl.debug = true
tbl.features[#tbl.features + 1] = "json_api"

-- Convert back to JSON
local out = json.stringify(tbl)
engine.log("Updated JSON:")
engine.log(out)
```
