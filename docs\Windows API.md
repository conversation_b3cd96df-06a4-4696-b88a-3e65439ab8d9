🖥️ winapi – Windows API Access

- winapi.get_tickcount64()
Returns the system uptime in milliseconds as a 64-bit integer.

- winapi.play_sound(file_name)
Plays a sound file (supports .wav and .mp3). The file can be a full path or a file located in the My Games directory.

- winapi.get_hwnd(class_name, window_name)
Returns a window handle (HWND) as an integer. Both parameters can be nil to perform a broad match.

- winapi.post_message(hwnd, msg, wparam, lparam)
Sends a Windows message to the given hwnd. All arguments must be integers. Returns a boolean indicating success.

- winapi.get_foreground_window()
Returns the handle (hwnd) of the foreground window.

- winapi.get_window_rect(hwnd)
Returns window position and size: left, top, right, bottom.

Example:
LUA:
```lua
local rect = winapi.get_window_rect(some_window)
local width = rect.right - rect.left
local height = rect.bottom - rect.top


- winapi.get_window_thread_process_id(hwnd)
Returns tid (thread ID) and pid (process ID).

Example:
LUA:

local info = winapi.get_window_thread_process_id(some_window)
local tid = info.tid
local pid = info.pid
```

- winapi.get_window_style(hwnd)
Returns the window style value as an integer.

- winapi.is_window_visible(hwnd)
Returns true if the window is visible.

- winapi.is_window_enabled(hwnd)
Returns true if the window is enabled (accepts input).