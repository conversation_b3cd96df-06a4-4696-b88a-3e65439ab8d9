
🔧 engine – Engine Functions API

🧹 Lifecycle Hooks

    engine.register_onunload(callback) – Registers a function to be called when the script is unloaded.
    engine.register_on_engine_tick(callback) – Registers a function to be called every engine tick.
    engine.register_on_network_callback(callback) – Registers a function to handle network responses.


🖨️ Logging

    engine.log(message, r, g, b, a) – Logs a message to the console with RGBA color.


🙍 User Info

    engine.get_username() – Returns the username of the currently logged-in user.


This example demonstrates how to register and use engine callbacks in Lua.

LUA:
```lua
-- Callback function for engine tick (executed every tick)
function enginetick_callback()
    -- Your logic here (executed on every engine tick)
end

-- Callback function for when the script is unloaded
function on_unload_callback()
    engine.log("Script is being unloaded", 255, 255, 255, 255)
end

-- Callback function for network responses
function on_net_callback(str)
    engine.log("Network Response: " .. str, 255, 255, 255, 255)
end

-- Register the callbacks with the engine
engine.register_on_engine_tick(enginetick_callback)
engine.register_onunload(on_unload_callback)
engine.register_on_network_callback(on_net_callback)
```


Example Usage:
- The enginetick_callback function executes continuously on every engine tick.
- The on_unload_callback function logs a message when the script is unloaded.
- The on_net_callback function logs incoming network responses.

This ensures smooth handling of engine updates, cleanup operations, and network events. 🚀
