
📅 Current Time and Formatting

    time.unix() – Returns the current Unix timestamp in seconds.
    time.unix_ms() – Returns the current Unix timestamp in milliseconds.
    time.now_utc() – Returns the current UTC time as a formatted string.
    time.now_local() – Returns the current local time as a formatted string.
    time.format(timestamp) – Converts a timestamp to a local date-time string.
    time.format_custom(timestamp, format) – Formats timestamp using a custom format string (UTC).



⏱️ Comparison and Difference

    time.delta(t1, t2) – Returns the difference between timestamps in seconds.
    time.compare(t1, t2) – Compares two timestamps (returns -1, 0, or 1).
    time.same_day(t1, t2) – Returns true if both timestamps fall on the same calendar day.
    time.diff_table(t1, t2) – Returns breakdown of difference in days/hours/minutes/seconds.
    time.between(now, start, end) – Checks if a timestamp falls within a range.



📆 Date Info / Conversion

    time.weekday(timestamp) – Gets weekday (0 = Sunday to 6 = Saturday).
    time.day_of_year(timestamp) – Returns the day number within the year.
    time.year_month_day(timestamp) – Returns a table with year, month, and day.
    time.is_weekend(timestamp) – Returns true if timestamp is Saturday or Sunday.
    time.is_leap_year(timestamp) – Checks if the year is a leap year.
    time.days_in_month(year, month) – Returns number of days in a given month/year.
    time.timestamp_utc(y, m, d, h, min, s) – Converts date to a UTC timestamp.
    time.add_days(timestamp, days) – Adds (or subtracts) days to a timestamp.
    time.start_of_day(timestamp) – Returns timestamp for 00:00:00.
    time.end_of_day(timestamp) – Returns timestamp for 23:59:59.



📦 Tables and Structures

    time.to_table(timestamp) – Converts to a local date-time table.
    time.from_table(table) – Converts local date-time table to timestamp.
    time.to_utc_table(timestamp) – Converts to a UTC date-time table.
    time.from_utc_table(table) – Converts UTC date-time table to timestamp.



🧪 Validation / Info

    time.is_valid(timestamp) – Checks if timestamp is valid.
    time.is_dst(timestamp) – Returns true if timestamp is in DST.
    time.utc_offset() – Returns the local time offset from UTC in seconds.
    time.get_timezone() – Returns the time zone string (e.g., UTC-03:00).



🧮 Utilities and Constants

    time.seconds_to_hhmmss(seconds) – Converts seconds to HH:MM:SS format.
    time.SECONDS_PER_MINUTE, SECONDS_PER_HOUR, SECONDS_PER_DAY, DAYS_PER_WEEK – Common constants.
    time.WEEKDAY_NAMES – Table of weekday names (1 = Sunday, 7 = Saturday).
    time.MONTH_NAMES – Table of month names (1 = January, 12 = December).
    time.MONTH_DAYS – Days in each month (non-leap).
    time.MONTH_DAYS_LEAP – Days in each month (leap year).
    time.MONTH_NAME_TO_INDEX – Maps month names to numeric indexes.

