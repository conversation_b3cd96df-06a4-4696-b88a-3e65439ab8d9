
🌐 Using the Networking API

This example demonstrates how to send an HTTP request and handle network responses in Lua.

LUA:

-- Callback function to handle network responses
function network_callback(response_data,  url)
    fs.write_to_file_from_buffer("dump.txt", response_data)
    engine.log("Received network response: " .. m.read_string(response_data,0), 0, 255, 0, 255)
end

-- Register the network callback
engine.register_on_network_callback(network_callback)

-- Function to send an HTTP request
function send_example_request()
    local url = "https://example.com/api/data"
 
    -- Example headers
    local headers = "MyLuaClient/1.0";

    -- Example POST data
    local post_fields = "param1=value1&param2=value2"

    -- Send the HTTP request
    net.send_request(url, headers, post_fields)
 
    engine.log("Request sent to: " .. url, 255, 255, 255, 255)
end

-- Execute the function to send the request
send_example_request()


LUA:
```lua
--Using socket API
local function log_info(msg)
    engine.log(msg, 255,255,255,255)
end
local function log_error(msg)
    engine.log(msg, 255,  0,  0,255)
end
-- resolve hostname
local ip = net.resolve("google.com")
if not ip then
    log_error("resolve failed for google.com")
    return
end
log_info("Resolved google.com → " .. ip)
-- open plain‐text HTTP on port 80
local sock, err = net.create_socket(ip, 80)
if not sock then
    log_error("connect failed: " .. err)
    return
end
log_info("Connected to " .. ip .. ":80")
-- build & send GET /
local req = table.concat({
    "GET / HTTP/1.1",
    "Host: google.com",
    "Connection: close",
    "",
    ""
}, "\r\n")
local sent, serr = sock:send(req)
if not sent then
    log_error("send failed: " .. serr)
    sock:close()
    return
end
log_info("Sent " .. sent .. " bytes")
-- receive up to 4096 bytes
local chunk, rerr = sock:receive(4096)
if not chunk then
    log_error("recv failed: " .. rerr)
else
    -- only log the first 200 chars
    local snippet = chunk:sub(1,200)
    log_info("Response snippet:\n" .. snippet)
end
-- cleanup
sock:close()
log_info("Socket closed")
```


Explanation:
- engine.register_on_network_callback(callback)
Registers a function to handle network responses.
- net.send_request(url, headers, post_fields)
Sends an HTTP request with optional headers and POST data.
- net.resolve(hostname)
Looks up the first IPv4 or IPv6 address for hostname. Example : "google.com"
- net.create_socket(ip, port)
Opens a TCP connection and returns a socket table
- socket:send(data)
Sends the raw string "data" on the socket and returns number of bytes sent
- socket:receive(maxlen)
Reads up to "maxlen" bytes from the socket. Returns the received string or nil plus an error message if the connection closed or an error occurred.
- socket:close() 
Immediately closes the socket.
- net.base64_encode(lstring)
Returns encoded lstring
- net.base64_decode(lstring)
Returns decoded lstring

Example Usage:
1. The script registers a network callback that logs responses.
2. A request is sent to "https://example.com/api/data".
3. The request includes headers like "User-Agent" and "Authorization".
4. The request sends POST data with parameters.
5. Once the server responds, the callback function logs the response.

This example ensures smooth handling of HTTP requests and responses in Lua scripts! 🚀🌍
